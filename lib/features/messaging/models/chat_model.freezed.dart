// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chat_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ChatModel {

 String get id; String get name; String get avatarUrl; String get lastMessage; DateTime get lastMessageTime; bool get isGroup; List<String> get participants; bool get isHidden; bool get isBlocked; ChatPrivacySettings get privacySettings; int get unreadCount; MessageStatus get lastMessageStatus;
/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatModelCopyWith<ChatModel> get copyWith => _$ChatModelCopyWithImpl<ChatModel>(this as ChatModel, _$identity);

  /// Serializes this ChatModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.lastMessage, lastMessage) || other.lastMessage == lastMessage)&&(identical(other.lastMessageTime, lastMessageTime) || other.lastMessageTime == lastMessageTime)&&(identical(other.isGroup, isGroup) || other.isGroup == isGroup)&&const DeepCollectionEquality().equals(other.participants, participants)&&(identical(other.isHidden, isHidden) || other.isHidden == isHidden)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.privacySettings, privacySettings) || other.privacySettings == privacySettings)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount)&&(identical(other.lastMessageStatus, lastMessageStatus) || other.lastMessageStatus == lastMessageStatus));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,lastMessage,lastMessageTime,isGroup,const DeepCollectionEquality().hash(participants),isHidden,isBlocked,privacySettings,unreadCount,lastMessageStatus);

@override
String toString() {
  return 'ChatModel(id: $id, name: $name, avatarUrl: $avatarUrl, lastMessage: $lastMessage, lastMessageTime: $lastMessageTime, isGroup: $isGroup, participants: $participants, isHidden: $isHidden, isBlocked: $isBlocked, privacySettings: $privacySettings, unreadCount: $unreadCount, lastMessageStatus: $lastMessageStatus)';
}


}

/// @nodoc
abstract mixin class $ChatModelCopyWith<$Res>  {
  factory $ChatModelCopyWith(ChatModel value, $Res Function(ChatModel) _then) = _$ChatModelCopyWithImpl;
@useResult
$Res call({
 String id, String name, String avatarUrl, String lastMessage, DateTime lastMessageTime, bool isGroup, List<String> participants, bool isHidden, bool isBlocked, ChatPrivacySettings privacySettings, int unreadCount, MessageStatus lastMessageStatus
});


$ChatPrivacySettingsCopyWith<$Res> get privacySettings;

}
/// @nodoc
class _$ChatModelCopyWithImpl<$Res>
    implements $ChatModelCopyWith<$Res> {
  _$ChatModelCopyWithImpl(this._self, this._then);

  final ChatModel _self;
  final $Res Function(ChatModel) _then;

/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,Object? lastMessage = null,Object? lastMessageTime = null,Object? isGroup = null,Object? participants = null,Object? isHidden = null,Object? isBlocked = null,Object? privacySettings = null,Object? unreadCount = null,Object? lastMessageStatus = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,lastMessage: null == lastMessage ? _self.lastMessage : lastMessage // ignore: cast_nullable_to_non_nullable
as String,lastMessageTime: null == lastMessageTime ? _self.lastMessageTime : lastMessageTime // ignore: cast_nullable_to_non_nullable
as DateTime,isGroup: null == isGroup ? _self.isGroup : isGroup // ignore: cast_nullable_to_non_nullable
as bool,participants: null == participants ? _self.participants : participants // ignore: cast_nullable_to_non_nullable
as List<String>,isHidden: null == isHidden ? _self.isHidden : isHidden // ignore: cast_nullable_to_non_nullable
as bool,isBlocked: null == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool,privacySettings: null == privacySettings ? _self.privacySettings : privacySettings // ignore: cast_nullable_to_non_nullable
as ChatPrivacySettings,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,lastMessageStatus: null == lastMessageStatus ? _self.lastMessageStatus : lastMessageStatus // ignore: cast_nullable_to_non_nullable
as MessageStatus,
  ));
}
/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatPrivacySettingsCopyWith<$Res> get privacySettings {
  
  return $ChatPrivacySettingsCopyWith<$Res>(_self.privacySettings, (value) {
    return _then(_self.copyWith(privacySettings: value));
  });
}
}


/// Adds pattern-matching-related methods to [ChatModel].
extension ChatModelPatterns on ChatModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatModel value)  $default,){
final _that = this;
switch (_that) {
case _ChatModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatModel value)?  $default,){
final _that = this;
switch (_that) {
case _ChatModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl,  String lastMessage,  DateTime lastMessageTime,  bool isGroup,  List<String> participants,  bool isHidden,  bool isBlocked,  ChatPrivacySettings privacySettings,  int unreadCount,  MessageStatus lastMessageStatus)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatModel() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl,_that.lastMessage,_that.lastMessageTime,_that.isGroup,_that.participants,_that.isHidden,_that.isBlocked,_that.privacySettings,_that.unreadCount,_that.lastMessageStatus);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String avatarUrl,  String lastMessage,  DateTime lastMessageTime,  bool isGroup,  List<String> participants,  bool isHidden,  bool isBlocked,  ChatPrivacySettings privacySettings,  int unreadCount,  MessageStatus lastMessageStatus)  $default,) {final _that = this;
switch (_that) {
case _ChatModel():
return $default(_that.id,_that.name,_that.avatarUrl,_that.lastMessage,_that.lastMessageTime,_that.isGroup,_that.participants,_that.isHidden,_that.isBlocked,_that.privacySettings,_that.unreadCount,_that.lastMessageStatus);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String avatarUrl,  String lastMessage,  DateTime lastMessageTime,  bool isGroup,  List<String> participants,  bool isHidden,  bool isBlocked,  ChatPrivacySettings privacySettings,  int unreadCount,  MessageStatus lastMessageStatus)?  $default,) {final _that = this;
switch (_that) {
case _ChatModel() when $default != null:
return $default(_that.id,_that.name,_that.avatarUrl,_that.lastMessage,_that.lastMessageTime,_that.isGroup,_that.participants,_that.isHidden,_that.isBlocked,_that.privacySettings,_that.unreadCount,_that.lastMessageStatus);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatModel implements ChatModel {
  const _ChatModel({required this.id, required this.name, required this.avatarUrl, required this.lastMessage, required this.lastMessageTime, required this.isGroup, required final  List<String> participants, required this.isHidden, required this.isBlocked, required this.privacySettings, required this.unreadCount, required this.lastMessageStatus}): _participants = participants;
  factory _ChatModel.fromJson(Map<String, dynamic> json) => _$ChatModelFromJson(json);

@override final  String id;
@override final  String name;
@override final  String avatarUrl;
@override final  String lastMessage;
@override final  DateTime lastMessageTime;
@override final  bool isGroup;
 final  List<String> _participants;
@override List<String> get participants {
  if (_participants is EqualUnmodifiableListView) return _participants;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_participants);
}

@override final  bool isHidden;
@override final  bool isBlocked;
@override final  ChatPrivacySettings privacySettings;
@override final  int unreadCount;
@override final  MessageStatus lastMessageStatus;

/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatModelCopyWith<_ChatModel> get copyWith => __$ChatModelCopyWithImpl<_ChatModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatModel&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.avatarUrl, avatarUrl) || other.avatarUrl == avatarUrl)&&(identical(other.lastMessage, lastMessage) || other.lastMessage == lastMessage)&&(identical(other.lastMessageTime, lastMessageTime) || other.lastMessageTime == lastMessageTime)&&(identical(other.isGroup, isGroup) || other.isGroup == isGroup)&&const DeepCollectionEquality().equals(other._participants, _participants)&&(identical(other.isHidden, isHidden) || other.isHidden == isHidden)&&(identical(other.isBlocked, isBlocked) || other.isBlocked == isBlocked)&&(identical(other.privacySettings, privacySettings) || other.privacySettings == privacySettings)&&(identical(other.unreadCount, unreadCount) || other.unreadCount == unreadCount)&&(identical(other.lastMessageStatus, lastMessageStatus) || other.lastMessageStatus == lastMessageStatus));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,avatarUrl,lastMessage,lastMessageTime,isGroup,const DeepCollectionEquality().hash(_participants),isHidden,isBlocked,privacySettings,unreadCount,lastMessageStatus);

@override
String toString() {
  return 'ChatModel(id: $id, name: $name, avatarUrl: $avatarUrl, lastMessage: $lastMessage, lastMessageTime: $lastMessageTime, isGroup: $isGroup, participants: $participants, isHidden: $isHidden, isBlocked: $isBlocked, privacySettings: $privacySettings, unreadCount: $unreadCount, lastMessageStatus: $lastMessageStatus)';
}


}

/// @nodoc
abstract mixin class _$ChatModelCopyWith<$Res> implements $ChatModelCopyWith<$Res> {
  factory _$ChatModelCopyWith(_ChatModel value, $Res Function(_ChatModel) _then) = __$ChatModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String avatarUrl, String lastMessage, DateTime lastMessageTime, bool isGroup, List<String> participants, bool isHidden, bool isBlocked, ChatPrivacySettings privacySettings, int unreadCount, MessageStatus lastMessageStatus
});


@override $ChatPrivacySettingsCopyWith<$Res> get privacySettings;

}
/// @nodoc
class __$ChatModelCopyWithImpl<$Res>
    implements _$ChatModelCopyWith<$Res> {
  __$ChatModelCopyWithImpl(this._self, this._then);

  final _ChatModel _self;
  final $Res Function(_ChatModel) _then;

/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? avatarUrl = null,Object? lastMessage = null,Object? lastMessageTime = null,Object? isGroup = null,Object? participants = null,Object? isHidden = null,Object? isBlocked = null,Object? privacySettings = null,Object? unreadCount = null,Object? lastMessageStatus = null,}) {
  return _then(_ChatModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,avatarUrl: null == avatarUrl ? _self.avatarUrl : avatarUrl // ignore: cast_nullable_to_non_nullable
as String,lastMessage: null == lastMessage ? _self.lastMessage : lastMessage // ignore: cast_nullable_to_non_nullable
as String,lastMessageTime: null == lastMessageTime ? _self.lastMessageTime : lastMessageTime // ignore: cast_nullable_to_non_nullable
as DateTime,isGroup: null == isGroup ? _self.isGroup : isGroup // ignore: cast_nullable_to_non_nullable
as bool,participants: null == participants ? _self._participants : participants // ignore: cast_nullable_to_non_nullable
as List<String>,isHidden: null == isHidden ? _self.isHidden : isHidden // ignore: cast_nullable_to_non_nullable
as bool,isBlocked: null == isBlocked ? _self.isBlocked : isBlocked // ignore: cast_nullable_to_non_nullable
as bool,privacySettings: null == privacySettings ? _self.privacySettings : privacySettings // ignore: cast_nullable_to_non_nullable
as ChatPrivacySettings,unreadCount: null == unreadCount ? _self.unreadCount : unreadCount // ignore: cast_nullable_to_non_nullable
as int,lastMessageStatus: null == lastMessageStatus ? _self.lastMessageStatus : lastMessageStatus // ignore: cast_nullable_to_non_nullable
as MessageStatus,
  ));
}

/// Create a copy of ChatModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ChatPrivacySettingsCopyWith<$Res> get privacySettings {
  
  return $ChatPrivacySettingsCopyWith<$Res>(_self.privacySettings, (value) {
    return _then(_self.copyWith(privacySettings: value));
  });
}
}


/// @nodoc
mixin _$MessageModel {

 String get id; String get chatId; String get senderId; String get content; DateTime get timestamp; MessageType get type; MessageStatus get status; bool get isEdited; DateTime? get editedAt; List<MessageReaction> get reactions; SelfDestructSettings? get selfDestructSettings; List<String> get seenBy; bool get isDeleted; DateTime? get deletedAt;// Additional fields for enhanced messaging features
 String get senderName; String get senderAvatarUrl; String? get mediaUrl;// For images, videos, files
 String? get fileName;// For file attachments
 int? get fileSize;// File size in bytes
 String? get thumbnailUrl;// For video thumbnails
 Map<String, dynamic>? get metadata;
/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageModelCopyWith<MessageModel> get copyWith => _$MessageModelCopyWithImpl<MessageModel>(this as MessageModel, _$identity);

  /// Serializes this MessageModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.chatId, chatId) || other.chatId == chatId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.content, content) || other.content == content)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.isEdited, isEdited) || other.isEdited == isEdited)&&(identical(other.editedAt, editedAt) || other.editedAt == editedAt)&&const DeepCollectionEquality().equals(other.reactions, reactions)&&(identical(other.selfDestructSettings, selfDestructSettings) || other.selfDestructSettings == selfDestructSettings)&&const DeepCollectionEquality().equals(other.seenBy, seenBy)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.senderName, senderName) || other.senderName == senderName)&&(identical(other.senderAvatarUrl, senderAvatarUrl) || other.senderAvatarUrl == senderAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.fileName, fileName) || other.fileName == fileName)&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.thumbnailUrl, thumbnailUrl) || other.thumbnailUrl == thumbnailUrl)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,chatId,senderId,content,timestamp,type,status,isEdited,editedAt,const DeepCollectionEquality().hash(reactions),selfDestructSettings,const DeepCollectionEquality().hash(seenBy),isDeleted,deletedAt,senderName,senderAvatarUrl,mediaUrl,fileName,fileSize,thumbnailUrl,const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'MessageModel(id: $id, chatId: $chatId, senderId: $senderId, content: $content, timestamp: $timestamp, type: $type, status: $status, isEdited: $isEdited, editedAt: $editedAt, reactions: $reactions, selfDestructSettings: $selfDestructSettings, seenBy: $seenBy, isDeleted: $isDeleted, deletedAt: $deletedAt, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, mediaUrl: $mediaUrl, fileName: $fileName, fileSize: $fileSize, thumbnailUrl: $thumbnailUrl, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $MessageModelCopyWith<$Res>  {
  factory $MessageModelCopyWith(MessageModel value, $Res Function(MessageModel) _then) = _$MessageModelCopyWithImpl;
@useResult
$Res call({
 String id, String chatId, String senderId, String content, DateTime timestamp, MessageType type, MessageStatus status, bool isEdited, DateTime? editedAt, List<MessageReaction> reactions, SelfDestructSettings? selfDestructSettings, List<String> seenBy, bool isDeleted, DateTime? deletedAt, String senderName, String senderAvatarUrl, String? mediaUrl, String? fileName, int? fileSize, String? thumbnailUrl, Map<String, dynamic>? metadata
});


$SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings;

}
/// @nodoc
class _$MessageModelCopyWithImpl<$Res>
    implements $MessageModelCopyWith<$Res> {
  _$MessageModelCopyWithImpl(this._self, this._then);

  final MessageModel _self;
  final $Res Function(MessageModel) _then;

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? chatId = null,Object? senderId = null,Object? content = null,Object? timestamp = null,Object? type = null,Object? status = null,Object? isEdited = null,Object? editedAt = freezed,Object? reactions = null,Object? selfDestructSettings = freezed,Object? seenBy = null,Object? isDeleted = null,Object? deletedAt = freezed,Object? senderName = null,Object? senderAvatarUrl = null,Object? mediaUrl = freezed,Object? fileName = freezed,Object? fileSize = freezed,Object? thumbnailUrl = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,chatId: null == chatId ? _self.chatId : chatId // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as MessageStatus,isEdited: null == isEdited ? _self.isEdited : isEdited // ignore: cast_nullable_to_non_nullable
as bool,editedAt: freezed == editedAt ? _self.editedAt : editedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reactions: null == reactions ? _self.reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<MessageReaction>,selfDestructSettings: freezed == selfDestructSettings ? _self.selfDestructSettings : selfDestructSettings // ignore: cast_nullable_to_non_nullable
as SelfDestructSettings?,seenBy: null == seenBy ? _self.seenBy : seenBy // ignore: cast_nullable_to_non_nullable
as List<String>,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,senderName: null == senderName ? _self.senderName : senderName // ignore: cast_nullable_to_non_nullable
as String,senderAvatarUrl: null == senderAvatarUrl ? _self.senderAvatarUrl : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: freezed == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String?,fileName: freezed == fileName ? _self.fileName : fileName // ignore: cast_nullable_to_non_nullable
as String?,fileSize: freezed == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as int?,thumbnailUrl: freezed == thumbnailUrl ? _self.thumbnailUrl : thumbnailUrl // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings {
    if (_self.selfDestructSettings == null) {
    return null;
  }

  return $SelfDestructSettingsCopyWith<$Res>(_self.selfDestructSettings!, (value) {
    return _then(_self.copyWith(selfDestructSettings: value));
  });
}
}


/// Adds pattern-matching-related methods to [MessageModel].
extension MessageModelPatterns on MessageModel {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageModel value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageModel value)  $default,){
final _that = this;
switch (_that) {
case _MessageModel():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageModel value)?  $default,){
final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String chatId,  String senderId,  String content,  DateTime timestamp,  MessageType type,  MessageStatus status,  bool isEdited,  DateTime? editedAt,  List<MessageReaction> reactions,  SelfDestructSettings? selfDestructSettings,  List<String> seenBy,  bool isDeleted,  DateTime? deletedAt,  String senderName,  String senderAvatarUrl,  String? mediaUrl,  String? fileName,  int? fileSize,  String? thumbnailUrl,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that.id,_that.chatId,_that.senderId,_that.content,_that.timestamp,_that.type,_that.status,_that.isEdited,_that.editedAt,_that.reactions,_that.selfDestructSettings,_that.seenBy,_that.isDeleted,_that.deletedAt,_that.senderName,_that.senderAvatarUrl,_that.mediaUrl,_that.fileName,_that.fileSize,_that.thumbnailUrl,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String chatId,  String senderId,  String content,  DateTime timestamp,  MessageType type,  MessageStatus status,  bool isEdited,  DateTime? editedAt,  List<MessageReaction> reactions,  SelfDestructSettings? selfDestructSettings,  List<String> seenBy,  bool isDeleted,  DateTime? deletedAt,  String senderName,  String senderAvatarUrl,  String? mediaUrl,  String? fileName,  int? fileSize,  String? thumbnailUrl,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _MessageModel():
return $default(_that.id,_that.chatId,_that.senderId,_that.content,_that.timestamp,_that.type,_that.status,_that.isEdited,_that.editedAt,_that.reactions,_that.selfDestructSettings,_that.seenBy,_that.isDeleted,_that.deletedAt,_that.senderName,_that.senderAvatarUrl,_that.mediaUrl,_that.fileName,_that.fileSize,_that.thumbnailUrl,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String chatId,  String senderId,  String content,  DateTime timestamp,  MessageType type,  MessageStatus status,  bool isEdited,  DateTime? editedAt,  List<MessageReaction> reactions,  SelfDestructSettings? selfDestructSettings,  List<String> seenBy,  bool isDeleted,  DateTime? deletedAt,  String senderName,  String senderAvatarUrl,  String? mediaUrl,  String? fileName,  int? fileSize,  String? thumbnailUrl,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _MessageModel() when $default != null:
return $default(_that.id,_that.chatId,_that.senderId,_that.content,_that.timestamp,_that.type,_that.status,_that.isEdited,_that.editedAt,_that.reactions,_that.selfDestructSettings,_that.seenBy,_that.isDeleted,_that.deletedAt,_that.senderName,_that.senderAvatarUrl,_that.mediaUrl,_that.fileName,_that.fileSize,_that.thumbnailUrl,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageModel implements MessageModel {
  const _MessageModel({required this.id, required this.chatId, required this.senderId, required this.content, required this.timestamp, required this.type, required this.status, required this.isEdited, required this.editedAt, required final  List<MessageReaction> reactions, required this.selfDestructSettings, required final  List<String> seenBy, required this.isDeleted, required this.deletedAt, this.senderName = '', this.senderAvatarUrl = '', this.mediaUrl, this.fileName, this.fileSize, this.thumbnailUrl, final  Map<String, dynamic>? metadata}): _reactions = reactions,_seenBy = seenBy,_metadata = metadata;
  factory _MessageModel.fromJson(Map<String, dynamic> json) => _$MessageModelFromJson(json);

@override final  String id;
@override final  String chatId;
@override final  String senderId;
@override final  String content;
@override final  DateTime timestamp;
@override final  MessageType type;
@override final  MessageStatus status;
@override final  bool isEdited;
@override final  DateTime? editedAt;
 final  List<MessageReaction> _reactions;
@override List<MessageReaction> get reactions {
  if (_reactions is EqualUnmodifiableListView) return _reactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_reactions);
}

@override final  SelfDestructSettings? selfDestructSettings;
 final  List<String> _seenBy;
@override List<String> get seenBy {
  if (_seenBy is EqualUnmodifiableListView) return _seenBy;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_seenBy);
}

@override final  bool isDeleted;
@override final  DateTime? deletedAt;
// Additional fields for enhanced messaging features
@override@JsonKey() final  String senderName;
@override@JsonKey() final  String senderAvatarUrl;
@override final  String? mediaUrl;
// For images, videos, files
@override final  String? fileName;
// For file attachments
@override final  int? fileSize;
// File size in bytes
@override final  String? thumbnailUrl;
// For video thumbnails
 final  Map<String, dynamic>? _metadata;
// For video thumbnails
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageModelCopyWith<_MessageModel> get copyWith => __$MessageModelCopyWithImpl<_MessageModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageModel&&(identical(other.id, id) || other.id == id)&&(identical(other.chatId, chatId) || other.chatId == chatId)&&(identical(other.senderId, senderId) || other.senderId == senderId)&&(identical(other.content, content) || other.content == content)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.type, type) || other.type == type)&&(identical(other.status, status) || other.status == status)&&(identical(other.isEdited, isEdited) || other.isEdited == isEdited)&&(identical(other.editedAt, editedAt) || other.editedAt == editedAt)&&const DeepCollectionEquality().equals(other._reactions, _reactions)&&(identical(other.selfDestructSettings, selfDestructSettings) || other.selfDestructSettings == selfDestructSettings)&&const DeepCollectionEquality().equals(other._seenBy, _seenBy)&&(identical(other.isDeleted, isDeleted) || other.isDeleted == isDeleted)&&(identical(other.deletedAt, deletedAt) || other.deletedAt == deletedAt)&&(identical(other.senderName, senderName) || other.senderName == senderName)&&(identical(other.senderAvatarUrl, senderAvatarUrl) || other.senderAvatarUrl == senderAvatarUrl)&&(identical(other.mediaUrl, mediaUrl) || other.mediaUrl == mediaUrl)&&(identical(other.fileName, fileName) || other.fileName == fileName)&&(identical(other.fileSize, fileSize) || other.fileSize == fileSize)&&(identical(other.thumbnailUrl, thumbnailUrl) || other.thumbnailUrl == thumbnailUrl)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,chatId,senderId,content,timestamp,type,status,isEdited,editedAt,const DeepCollectionEquality().hash(_reactions),selfDestructSettings,const DeepCollectionEquality().hash(_seenBy),isDeleted,deletedAt,senderName,senderAvatarUrl,mediaUrl,fileName,fileSize,thumbnailUrl,const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'MessageModel(id: $id, chatId: $chatId, senderId: $senderId, content: $content, timestamp: $timestamp, type: $type, status: $status, isEdited: $isEdited, editedAt: $editedAt, reactions: $reactions, selfDestructSettings: $selfDestructSettings, seenBy: $seenBy, isDeleted: $isDeleted, deletedAt: $deletedAt, senderName: $senderName, senderAvatarUrl: $senderAvatarUrl, mediaUrl: $mediaUrl, fileName: $fileName, fileSize: $fileSize, thumbnailUrl: $thumbnailUrl, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$MessageModelCopyWith<$Res> implements $MessageModelCopyWith<$Res> {
  factory _$MessageModelCopyWith(_MessageModel value, $Res Function(_MessageModel) _then) = __$MessageModelCopyWithImpl;
@override @useResult
$Res call({
 String id, String chatId, String senderId, String content, DateTime timestamp, MessageType type, MessageStatus status, bool isEdited, DateTime? editedAt, List<MessageReaction> reactions, SelfDestructSettings? selfDestructSettings, List<String> seenBy, bool isDeleted, DateTime? deletedAt, String senderName, String senderAvatarUrl, String? mediaUrl, String? fileName, int? fileSize, String? thumbnailUrl, Map<String, dynamic>? metadata
});


@override $SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings;

}
/// @nodoc
class __$MessageModelCopyWithImpl<$Res>
    implements _$MessageModelCopyWith<$Res> {
  __$MessageModelCopyWithImpl(this._self, this._then);

  final _MessageModel _self;
  final $Res Function(_MessageModel) _then;

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? chatId = null,Object? senderId = null,Object? content = null,Object? timestamp = null,Object? type = null,Object? status = null,Object? isEdited = null,Object? editedAt = freezed,Object? reactions = null,Object? selfDestructSettings = freezed,Object? seenBy = null,Object? isDeleted = null,Object? deletedAt = freezed,Object? senderName = null,Object? senderAvatarUrl = null,Object? mediaUrl = freezed,Object? fileName = freezed,Object? fileSize = freezed,Object? thumbnailUrl = freezed,Object? metadata = freezed,}) {
  return _then(_MessageModel(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,chatId: null == chatId ? _self.chatId : chatId // ignore: cast_nullable_to_non_nullable
as String,senderId: null == senderId ? _self.senderId : senderId // ignore: cast_nullable_to_non_nullable
as String,content: null == content ? _self.content : content // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as MessageType,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as MessageStatus,isEdited: null == isEdited ? _self.isEdited : isEdited // ignore: cast_nullable_to_non_nullable
as bool,editedAt: freezed == editedAt ? _self.editedAt : editedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reactions: null == reactions ? _self._reactions : reactions // ignore: cast_nullable_to_non_nullable
as List<MessageReaction>,selfDestructSettings: freezed == selfDestructSettings ? _self.selfDestructSettings : selfDestructSettings // ignore: cast_nullable_to_non_nullable
as SelfDestructSettings?,seenBy: null == seenBy ? _self._seenBy : seenBy // ignore: cast_nullable_to_non_nullable
as List<String>,isDeleted: null == isDeleted ? _self.isDeleted : isDeleted // ignore: cast_nullable_to_non_nullable
as bool,deletedAt: freezed == deletedAt ? _self.deletedAt : deletedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,senderName: null == senderName ? _self.senderName : senderName // ignore: cast_nullable_to_non_nullable
as String,senderAvatarUrl: null == senderAvatarUrl ? _self.senderAvatarUrl : senderAvatarUrl // ignore: cast_nullable_to_non_nullable
as String,mediaUrl: freezed == mediaUrl ? _self.mediaUrl : mediaUrl // ignore: cast_nullable_to_non_nullable
as String?,fileName: freezed == fileName ? _self.fileName : fileName // ignore: cast_nullable_to_non_nullable
as String?,fileSize: freezed == fileSize ? _self.fileSize : fileSize // ignore: cast_nullable_to_non_nullable
as int?,thumbnailUrl: freezed == thumbnailUrl ? _self.thumbnailUrl : thumbnailUrl // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of MessageModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$SelfDestructSettingsCopyWith<$Res>? get selfDestructSettings {
    if (_self.selfDestructSettings == null) {
    return null;
  }

  return $SelfDestructSettingsCopyWith<$Res>(_self.selfDestructSettings!, (value) {
    return _then(_self.copyWith(selfDestructSettings: value));
  });
}
}


/// @nodoc
mixin _$MessageReaction {

 String get userId; String get emoji; DateTime get timestamp;
/// Create a copy of MessageReaction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MessageReactionCopyWith<MessageReaction> get copyWith => _$MessageReactionCopyWithImpl<MessageReaction>(this as MessageReaction, _$identity);

  /// Serializes this MessageReaction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MessageReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.emoji, emoji) || other.emoji == emoji)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,emoji,timestamp);

@override
String toString() {
  return 'MessageReaction(userId: $userId, emoji: $emoji, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class $MessageReactionCopyWith<$Res>  {
  factory $MessageReactionCopyWith(MessageReaction value, $Res Function(MessageReaction) _then) = _$MessageReactionCopyWithImpl;
@useResult
$Res call({
 String userId, String emoji, DateTime timestamp
});




}
/// @nodoc
class _$MessageReactionCopyWithImpl<$Res>
    implements $MessageReactionCopyWith<$Res> {
  _$MessageReactionCopyWithImpl(this._self, this._then);

  final MessageReaction _self;
  final $Res Function(MessageReaction) _then;

/// Create a copy of MessageReaction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? emoji = null,Object? timestamp = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,emoji: null == emoji ? _self.emoji : emoji // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [MessageReaction].
extension MessageReactionPatterns on MessageReaction {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _MessageReaction value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _MessageReaction() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _MessageReaction value)  $default,){
final _that = this;
switch (_that) {
case _MessageReaction():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _MessageReaction value)?  $default,){
final _that = this;
switch (_that) {
case _MessageReaction() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  String emoji,  DateTime timestamp)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _MessageReaction() when $default != null:
return $default(_that.userId,_that.emoji,_that.timestamp);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  String emoji,  DateTime timestamp)  $default,) {final _that = this;
switch (_that) {
case _MessageReaction():
return $default(_that.userId,_that.emoji,_that.timestamp);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  String emoji,  DateTime timestamp)?  $default,) {final _that = this;
switch (_that) {
case _MessageReaction() when $default != null:
return $default(_that.userId,_that.emoji,_that.timestamp);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _MessageReaction implements MessageReaction {
  const _MessageReaction({required this.userId, required this.emoji, required this.timestamp});
  factory _MessageReaction.fromJson(Map<String, dynamic> json) => _$MessageReactionFromJson(json);

@override final  String userId;
@override final  String emoji;
@override final  DateTime timestamp;

/// Create a copy of MessageReaction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MessageReactionCopyWith<_MessageReaction> get copyWith => __$MessageReactionCopyWithImpl<_MessageReaction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MessageReactionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MessageReaction&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.emoji, emoji) || other.emoji == emoji)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,emoji,timestamp);

@override
String toString() {
  return 'MessageReaction(userId: $userId, emoji: $emoji, timestamp: $timestamp)';
}


}

/// @nodoc
abstract mixin class _$MessageReactionCopyWith<$Res> implements $MessageReactionCopyWith<$Res> {
  factory _$MessageReactionCopyWith(_MessageReaction value, $Res Function(_MessageReaction) _then) = __$MessageReactionCopyWithImpl;
@override @useResult
$Res call({
 String userId, String emoji, DateTime timestamp
});




}
/// @nodoc
class __$MessageReactionCopyWithImpl<$Res>
    implements _$MessageReactionCopyWith<$Res> {
  __$MessageReactionCopyWithImpl(this._self, this._then);

  final _MessageReaction _self;
  final $Res Function(_MessageReaction) _then;

/// Create a copy of MessageReaction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? emoji = null,Object? timestamp = null,}) {
  return _then(_MessageReaction(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,emoji: null == emoji ? _self.emoji : emoji // ignore: cast_nullable_to_non_nullable
as String,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}


/// @nodoc
mixin _$SelfDestructSettings {

 SelfDestructType get type; int get durationInSeconds; DateTime? get expiresAt;
/// Create a copy of SelfDestructSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SelfDestructSettingsCopyWith<SelfDestructSettings> get copyWith => _$SelfDestructSettingsCopyWithImpl<SelfDestructSettings>(this as SelfDestructSettings, _$identity);

  /// Serializes this SelfDestructSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SelfDestructSettings&&(identical(other.type, type) || other.type == type)&&(identical(other.durationInSeconds, durationInSeconds) || other.durationInSeconds == durationInSeconds)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,durationInSeconds,expiresAt);

@override
String toString() {
  return 'SelfDestructSettings(type: $type, durationInSeconds: $durationInSeconds, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class $SelfDestructSettingsCopyWith<$Res>  {
  factory $SelfDestructSettingsCopyWith(SelfDestructSettings value, $Res Function(SelfDestructSettings) _then) = _$SelfDestructSettingsCopyWithImpl;
@useResult
$Res call({
 SelfDestructType type, int durationInSeconds, DateTime? expiresAt
});




}
/// @nodoc
class _$SelfDestructSettingsCopyWithImpl<$Res>
    implements $SelfDestructSettingsCopyWith<$Res> {
  _$SelfDestructSettingsCopyWithImpl(this._self, this._then);

  final SelfDestructSettings _self;
  final $Res Function(SelfDestructSettings) _then;

/// Create a copy of SelfDestructSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? durationInSeconds = null,Object? expiresAt = freezed,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SelfDestructType,durationInSeconds: null == durationInSeconds ? _self.durationInSeconds : durationInSeconds // ignore: cast_nullable_to_non_nullable
as int,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

}


/// Adds pattern-matching-related methods to [SelfDestructSettings].
extension SelfDestructSettingsPatterns on SelfDestructSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _SelfDestructSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _SelfDestructSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _SelfDestructSettings value)  $default,){
final _that = this;
switch (_that) {
case _SelfDestructSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _SelfDestructSettings value)?  $default,){
final _that = this;
switch (_that) {
case _SelfDestructSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( SelfDestructType type,  int durationInSeconds,  DateTime? expiresAt)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _SelfDestructSettings() when $default != null:
return $default(_that.type,_that.durationInSeconds,_that.expiresAt);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( SelfDestructType type,  int durationInSeconds,  DateTime? expiresAt)  $default,) {final _that = this;
switch (_that) {
case _SelfDestructSettings():
return $default(_that.type,_that.durationInSeconds,_that.expiresAt);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( SelfDestructType type,  int durationInSeconds,  DateTime? expiresAt)?  $default,) {final _that = this;
switch (_that) {
case _SelfDestructSettings() when $default != null:
return $default(_that.type,_that.durationInSeconds,_that.expiresAt);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _SelfDestructSettings implements SelfDestructSettings {
  const _SelfDestructSettings({required this.type, required this.durationInSeconds, required this.expiresAt});
  factory _SelfDestructSettings.fromJson(Map<String, dynamic> json) => _$SelfDestructSettingsFromJson(json);

@override final  SelfDestructType type;
@override final  int durationInSeconds;
@override final  DateTime? expiresAt;

/// Create a copy of SelfDestructSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SelfDestructSettingsCopyWith<_SelfDestructSettings> get copyWith => __$SelfDestructSettingsCopyWithImpl<_SelfDestructSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$SelfDestructSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SelfDestructSettings&&(identical(other.type, type) || other.type == type)&&(identical(other.durationInSeconds, durationInSeconds) || other.durationInSeconds == durationInSeconds)&&(identical(other.expiresAt, expiresAt) || other.expiresAt == expiresAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,durationInSeconds,expiresAt);

@override
String toString() {
  return 'SelfDestructSettings(type: $type, durationInSeconds: $durationInSeconds, expiresAt: $expiresAt)';
}


}

/// @nodoc
abstract mixin class _$SelfDestructSettingsCopyWith<$Res> implements $SelfDestructSettingsCopyWith<$Res> {
  factory _$SelfDestructSettingsCopyWith(_SelfDestructSettings value, $Res Function(_SelfDestructSettings) _then) = __$SelfDestructSettingsCopyWithImpl;
@override @useResult
$Res call({
 SelfDestructType type, int durationInSeconds, DateTime? expiresAt
});




}
/// @nodoc
class __$SelfDestructSettingsCopyWithImpl<$Res>
    implements _$SelfDestructSettingsCopyWith<$Res> {
  __$SelfDestructSettingsCopyWithImpl(this._self, this._then);

  final _SelfDestructSettings _self;
  final $Res Function(_SelfDestructSettings) _then;

/// Create a copy of SelfDestructSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? durationInSeconds = null,Object? expiresAt = freezed,}) {
  return _then(_SelfDestructSettings(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as SelfDestructType,durationInSeconds: null == durationInSeconds ? _self.durationInSeconds : durationInSeconds // ignore: cast_nullable_to_non_nullable
as int,expiresAt: freezed == expiresAt ? _self.expiresAt : expiresAt // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}


}


/// @nodoc
mixin _$ChatPrivacySettings {

 bool get blockScreenshots; bool get screenshotAlerts; bool get readReceipts; bool get typingIndicators; bool get messageReactions; bool get selfDestructEnabled; int get defaultSelfDestructSeconds;
/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ChatPrivacySettingsCopyWith<ChatPrivacySettings> get copyWith => _$ChatPrivacySettingsCopyWithImpl<ChatPrivacySettings>(this as ChatPrivacySettings, _$identity);

  /// Serializes this ChatPrivacySettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ChatPrivacySettings&&(identical(other.blockScreenshots, blockScreenshots) || other.blockScreenshots == blockScreenshots)&&(identical(other.screenshotAlerts, screenshotAlerts) || other.screenshotAlerts == screenshotAlerts)&&(identical(other.readReceipts, readReceipts) || other.readReceipts == readReceipts)&&(identical(other.typingIndicators, typingIndicators) || other.typingIndicators == typingIndicators)&&(identical(other.messageReactions, messageReactions) || other.messageReactions == messageReactions)&&(identical(other.selfDestructEnabled, selfDestructEnabled) || other.selfDestructEnabled == selfDestructEnabled)&&(identical(other.defaultSelfDestructSeconds, defaultSelfDestructSeconds) || other.defaultSelfDestructSeconds == defaultSelfDestructSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,blockScreenshots,screenshotAlerts,readReceipts,typingIndicators,messageReactions,selfDestructEnabled,defaultSelfDestructSeconds);

@override
String toString() {
  return 'ChatPrivacySettings(blockScreenshots: $blockScreenshots, screenshotAlerts: $screenshotAlerts, readReceipts: $readReceipts, typingIndicators: $typingIndicators, messageReactions: $messageReactions, selfDestructEnabled: $selfDestructEnabled, defaultSelfDestructSeconds: $defaultSelfDestructSeconds)';
}


}

/// @nodoc
abstract mixin class $ChatPrivacySettingsCopyWith<$Res>  {
  factory $ChatPrivacySettingsCopyWith(ChatPrivacySettings value, $Res Function(ChatPrivacySettings) _then) = _$ChatPrivacySettingsCopyWithImpl;
@useResult
$Res call({
 bool blockScreenshots, bool screenshotAlerts, bool readReceipts, bool typingIndicators, bool messageReactions, bool selfDestructEnabled, int defaultSelfDestructSeconds
});




}
/// @nodoc
class _$ChatPrivacySettingsCopyWithImpl<$Res>
    implements $ChatPrivacySettingsCopyWith<$Res> {
  _$ChatPrivacySettingsCopyWithImpl(this._self, this._then);

  final ChatPrivacySettings _self;
  final $Res Function(ChatPrivacySettings) _then;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? blockScreenshots = null,Object? screenshotAlerts = null,Object? readReceipts = null,Object? typingIndicators = null,Object? messageReactions = null,Object? selfDestructEnabled = null,Object? defaultSelfDestructSeconds = null,}) {
  return _then(_self.copyWith(
blockScreenshots: null == blockScreenshots ? _self.blockScreenshots : blockScreenshots // ignore: cast_nullable_to_non_nullable
as bool,screenshotAlerts: null == screenshotAlerts ? _self.screenshotAlerts : screenshotAlerts // ignore: cast_nullable_to_non_nullable
as bool,readReceipts: null == readReceipts ? _self.readReceipts : readReceipts // ignore: cast_nullable_to_non_nullable
as bool,typingIndicators: null == typingIndicators ? _self.typingIndicators : typingIndicators // ignore: cast_nullable_to_non_nullable
as bool,messageReactions: null == messageReactions ? _self.messageReactions : messageReactions // ignore: cast_nullable_to_non_nullable
as bool,selfDestructEnabled: null == selfDestructEnabled ? _self.selfDestructEnabled : selfDestructEnabled // ignore: cast_nullable_to_non_nullable
as bool,defaultSelfDestructSeconds: null == defaultSelfDestructSeconds ? _self.defaultSelfDestructSeconds : defaultSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// Adds pattern-matching-related methods to [ChatPrivacySettings].
extension ChatPrivacySettingsPatterns on ChatPrivacySettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _ChatPrivacySettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _ChatPrivacySettings value)  $default,){
final _that = this;
switch (_that) {
case _ChatPrivacySettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _ChatPrivacySettings value)?  $default,){
final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool blockScreenshots,  bool screenshotAlerts,  bool readReceipts,  bool typingIndicators,  bool messageReactions,  bool selfDestructEnabled,  int defaultSelfDestructSeconds)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that.blockScreenshots,_that.screenshotAlerts,_that.readReceipts,_that.typingIndicators,_that.messageReactions,_that.selfDestructEnabled,_that.defaultSelfDestructSeconds);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool blockScreenshots,  bool screenshotAlerts,  bool readReceipts,  bool typingIndicators,  bool messageReactions,  bool selfDestructEnabled,  int defaultSelfDestructSeconds)  $default,) {final _that = this;
switch (_that) {
case _ChatPrivacySettings():
return $default(_that.blockScreenshots,_that.screenshotAlerts,_that.readReceipts,_that.typingIndicators,_that.messageReactions,_that.selfDestructEnabled,_that.defaultSelfDestructSeconds);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool blockScreenshots,  bool screenshotAlerts,  bool readReceipts,  bool typingIndicators,  bool messageReactions,  bool selfDestructEnabled,  int defaultSelfDestructSeconds)?  $default,) {final _that = this;
switch (_that) {
case _ChatPrivacySettings() when $default != null:
return $default(_that.blockScreenshots,_that.screenshotAlerts,_that.readReceipts,_that.typingIndicators,_that.messageReactions,_that.selfDestructEnabled,_that.defaultSelfDestructSeconds);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _ChatPrivacySettings implements ChatPrivacySettings {
  const _ChatPrivacySettings({required this.blockScreenshots, required this.screenshotAlerts, required this.readReceipts, required this.typingIndicators, required this.messageReactions, required this.selfDestructEnabled, required this.defaultSelfDestructSeconds});
  factory _ChatPrivacySettings.fromJson(Map<String, dynamic> json) => _$ChatPrivacySettingsFromJson(json);

@override final  bool blockScreenshots;
@override final  bool screenshotAlerts;
@override final  bool readReceipts;
@override final  bool typingIndicators;
@override final  bool messageReactions;
@override final  bool selfDestructEnabled;
@override final  int defaultSelfDestructSeconds;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChatPrivacySettingsCopyWith<_ChatPrivacySettings> get copyWith => __$ChatPrivacySettingsCopyWithImpl<_ChatPrivacySettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ChatPrivacySettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChatPrivacySettings&&(identical(other.blockScreenshots, blockScreenshots) || other.blockScreenshots == blockScreenshots)&&(identical(other.screenshotAlerts, screenshotAlerts) || other.screenshotAlerts == screenshotAlerts)&&(identical(other.readReceipts, readReceipts) || other.readReceipts == readReceipts)&&(identical(other.typingIndicators, typingIndicators) || other.typingIndicators == typingIndicators)&&(identical(other.messageReactions, messageReactions) || other.messageReactions == messageReactions)&&(identical(other.selfDestructEnabled, selfDestructEnabled) || other.selfDestructEnabled == selfDestructEnabled)&&(identical(other.defaultSelfDestructSeconds, defaultSelfDestructSeconds) || other.defaultSelfDestructSeconds == defaultSelfDestructSeconds));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,blockScreenshots,screenshotAlerts,readReceipts,typingIndicators,messageReactions,selfDestructEnabled,defaultSelfDestructSeconds);

@override
String toString() {
  return 'ChatPrivacySettings(blockScreenshots: $blockScreenshots, screenshotAlerts: $screenshotAlerts, readReceipts: $readReceipts, typingIndicators: $typingIndicators, messageReactions: $messageReactions, selfDestructEnabled: $selfDestructEnabled, defaultSelfDestructSeconds: $defaultSelfDestructSeconds)';
}


}

/// @nodoc
abstract mixin class _$ChatPrivacySettingsCopyWith<$Res> implements $ChatPrivacySettingsCopyWith<$Res> {
  factory _$ChatPrivacySettingsCopyWith(_ChatPrivacySettings value, $Res Function(_ChatPrivacySettings) _then) = __$ChatPrivacySettingsCopyWithImpl;
@override @useResult
$Res call({
 bool blockScreenshots, bool screenshotAlerts, bool readReceipts, bool typingIndicators, bool messageReactions, bool selfDestructEnabled, int defaultSelfDestructSeconds
});




}
/// @nodoc
class __$ChatPrivacySettingsCopyWithImpl<$Res>
    implements _$ChatPrivacySettingsCopyWith<$Res> {
  __$ChatPrivacySettingsCopyWithImpl(this._self, this._then);

  final _ChatPrivacySettings _self;
  final $Res Function(_ChatPrivacySettings) _then;

/// Create a copy of ChatPrivacySettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? blockScreenshots = null,Object? screenshotAlerts = null,Object? readReceipts = null,Object? typingIndicators = null,Object? messageReactions = null,Object? selfDestructEnabled = null,Object? defaultSelfDestructSeconds = null,}) {
  return _then(_ChatPrivacySettings(
blockScreenshots: null == blockScreenshots ? _self.blockScreenshots : blockScreenshots // ignore: cast_nullable_to_non_nullable
as bool,screenshotAlerts: null == screenshotAlerts ? _self.screenshotAlerts : screenshotAlerts // ignore: cast_nullable_to_non_nullable
as bool,readReceipts: null == readReceipts ? _self.readReceipts : readReceipts // ignore: cast_nullable_to_non_nullable
as bool,typingIndicators: null == typingIndicators ? _self.typingIndicators : typingIndicators // ignore: cast_nullable_to_non_nullable
as bool,messageReactions: null == messageReactions ? _self.messageReactions : messageReactions // ignore: cast_nullable_to_non_nullable
as bool,selfDestructEnabled: null == selfDestructEnabled ? _self.selfDestructEnabled : selfDestructEnabled // ignore: cast_nullable_to_non_nullable
as bool,defaultSelfDestructSeconds: null == defaultSelfDestructSeconds ? _self.defaultSelfDestructSeconds : defaultSelfDestructSeconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on
