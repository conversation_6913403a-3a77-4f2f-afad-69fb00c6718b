// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ChatModel _$ChatModelFromJson(Map<String, dynamic> json) => _ChatModel(
  id: json['id'] as String,
  name: json['name'] as String,
  avatarUrl: json['avatarUrl'] as String,
  lastMessage: json['lastMessage'] as String,
  lastMessageTime: DateTime.parse(json['lastMessageTime'] as String),
  isGroup: json['isGroup'] as bool,
  participants: (json['participants'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  isHidden: json['isHidden'] as bool,
  isBlocked: json['isBlocked'] as bool,
  privacySettings: ChatPrivacySettings.fromJson(
    json['privacySettings'] as Map<String, dynamic>,
  ),
  unreadCount: (json['unreadCount'] as num).toInt(),
  lastMessageStatus: $enumDecode(
    _$MessageStatusEnumMap,
    json['lastMessageStatus'],
  ),
);

Map<String, dynamic> _$ChatModelToJson(_ChatModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'lastMessage': instance.lastMessage,
      'lastMessageTime': instance.lastMessageTime.toIso8601String(),
      'isGroup': instance.isGroup,
      'participants': instance.participants,
      'isHidden': instance.isHidden,
      'isBlocked': instance.isBlocked,
      'privacySettings': instance.privacySettings,
      'unreadCount': instance.unreadCount,
      'lastMessageStatus': _$MessageStatusEnumMap[instance.lastMessageStatus]!,
    };

const _$MessageStatusEnumMap = {
  MessageStatus.sending: 'sending',
  MessageStatus.sent: 'sent',
  MessageStatus.delivered: 'delivered',
  MessageStatus.read: 'read',
  MessageStatus.failed: 'failed',
};

_MessageModel _$MessageModelFromJson(Map<String, dynamic> json) =>
    _MessageModel(
      id: json['id'] as String,
      chatId: json['chatId'] as String,
      senderId: json['senderId'] as String,
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: $enumDecode(_$MessageTypeEnumMap, json['type']),
      status: $enumDecode(_$MessageStatusEnumMap, json['status']),
      isEdited: json['isEdited'] as bool,
      editedAt: json['editedAt'] == null
          ? null
          : DateTime.parse(json['editedAt'] as String),
      reactions: (json['reactions'] as List<dynamic>)
          .map((e) => MessageReaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      selfDestructSettings: json['selfDestructSettings'] == null
          ? null
          : SelfDestructSettings.fromJson(
              json['selfDestructSettings'] as Map<String, dynamic>,
            ),
      seenBy: (json['seenBy'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      isDeleted: json['isDeleted'] as bool,
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      senderName: json['senderName'] as String? ?? '',
      senderAvatarUrl: json['senderAvatarUrl'] as String? ?? '',
      mediaUrl: json['mediaUrl'] as String?,
      fileName: json['fileName'] as String?,
      fileSize: (json['fileSize'] as num?)?.toInt(),
      thumbnailUrl: json['thumbnailUrl'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$MessageModelToJson(_MessageModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'chatId': instance.chatId,
      'senderId': instance.senderId,
      'content': instance.content,
      'timestamp': instance.timestamp.toIso8601String(),
      'type': _$MessageTypeEnumMap[instance.type]!,
      'status': _$MessageStatusEnumMap[instance.status]!,
      'isEdited': instance.isEdited,
      'editedAt': instance.editedAt?.toIso8601String(),
      'reactions': instance.reactions,
      'selfDestructSettings': instance.selfDestructSettings,
      'seenBy': instance.seenBy,
      'isDeleted': instance.isDeleted,
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'senderName': instance.senderName,
      'senderAvatarUrl': instance.senderAvatarUrl,
      'mediaUrl': instance.mediaUrl,
      'fileName': instance.fileName,
      'fileSize': instance.fileSize,
      'thumbnailUrl': instance.thumbnailUrl,
      'metadata': instance.metadata,
    };

const _$MessageTypeEnumMap = {
  MessageType.text: 'text',
  MessageType.image: 'image',
  MessageType.video: 'video',
  MessageType.audio: 'audio',
  MessageType.file: 'file',
  MessageType.location: 'location',
  MessageType.contact: 'contact',
};

_MessageReaction _$MessageReactionFromJson(Map<String, dynamic> json) =>
    _MessageReaction(
      userId: json['userId'] as String,
      emoji: json['emoji'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$MessageReactionToJson(_MessageReaction instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'emoji': instance.emoji,
      'timestamp': instance.timestamp.toIso8601String(),
    };

_SelfDestructSettings _$SelfDestructSettingsFromJson(
  Map<String, dynamic> json,
) => _SelfDestructSettings(
  type: $enumDecode(_$SelfDestructTypeEnumMap, json['type']),
  durationInSeconds: (json['durationInSeconds'] as num).toInt(),
  expiresAt: json['expiresAt'] == null
      ? null
      : DateTime.parse(json['expiresAt'] as String),
);

Map<String, dynamic> _$SelfDestructSettingsToJson(
  _SelfDestructSettings instance,
) => <String, dynamic>{
  'type': _$SelfDestructTypeEnumMap[instance.type]!,
  'durationInSeconds': instance.durationInSeconds,
  'expiresAt': instance.expiresAt?.toIso8601String(),
};

const _$SelfDestructTypeEnumMap = {
  SelfDestructType.afterRead: 'afterRead',
  SelfDestructType.afterTime: 'afterTime',
  SelfDestructType.afterReply: 'afterReply',
};

_ChatPrivacySettings _$ChatPrivacySettingsFromJson(Map<String, dynamic> json) =>
    _ChatPrivacySettings(
      blockScreenshots: json['blockScreenshots'] as bool,
      screenshotAlerts: json['screenshotAlerts'] as bool,
      readReceipts: json['readReceipts'] as bool,
      typingIndicators: json['typingIndicators'] as bool,
      messageReactions: json['messageReactions'] as bool,
      selfDestructEnabled: json['selfDestructEnabled'] as bool,
      defaultSelfDestructSeconds: (json['defaultSelfDestructSeconds'] as num)
          .toInt(),
    );

Map<String, dynamic> _$ChatPrivacySettingsToJson(
  _ChatPrivacySettings instance,
) => <String, dynamic>{
  'blockScreenshots': instance.blockScreenshots,
  'screenshotAlerts': instance.screenshotAlerts,
  'readReceipts': instance.readReceipts,
  'typingIndicators': instance.typingIndicators,
  'messageReactions': instance.messageReactions,
  'selfDestructEnabled': instance.selfDestructEnabled,
  'defaultSelfDestructSeconds': instance.defaultSelfDestructSeconds,
};
