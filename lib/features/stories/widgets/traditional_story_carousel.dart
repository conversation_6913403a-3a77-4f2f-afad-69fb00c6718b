import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/stories/screens/story_viewer_screen.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';

/// Traditional horizontal story carousel similar to Instagram stories
class TraditionalStoryCarousel extends ConsumerStatefulWidget {
  final List<StoryReel> storyReels;
  final VoidCallback? onCreateStory;

  const TraditionalStoryCarousel({
    super.key,
    required this.storyReels,
    this.onCreateStory,
  });

  @override
  ConsumerState<TraditionalStoryCarousel> createState() =>
      _TraditionalStoryCarouselState();
}

class _TraditionalStoryCarouselState
    extends ConsumerState<TraditionalStoryCarousel> {
  int _selectedIndex = -1; // -1 means create button selected

  static const double _storySize = 60.0;
  static const double _createButtonSize = 60.0;

  // Handle tap on story
  void _onStoryTap(int index) {
    setState(() {
      _selectedIndex = index;
    });

    final reel = widget.storyReels[index];
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StoryViewerScreen(
          reel: reel,
          allReels: widget.storyReels,
          initialReelIndex: index,
        ),
      ),
    );
  }

  // Handle tap on create story button
  void _onCreateStoryTap() {
    setState(() {
      _selectedIndex = -1;
    });

    if (widget.onCreateStory != null) {
      widget.onCreateStory!();
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const StoryCreationScreen()),
      );
    }
  }

  // Build create story button
  Widget _buildCreateStoryButton() {
    final isSelected = _selectedIndex == -1;

    return GestureDetector(
      onTap: _onCreateStoryTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: _createButtonSize,
            height: _createButtonSize,
            transform: Matrix4.identity()..scale(isSelected ? 1.1 : 1.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              border: Border.all(
                color: isSelected ? Colors.amber[700]! : Colors.amber,
                width: 2,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.5),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ]
                  : null,
            ),
            child: const Icon(Icons.add, color: Colors.white, size: 28),
          ),
          const SizedBox(height: 4),
          const Text(
            'Your Story',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Build individual story item
  Widget _buildStoryItem(StoryReel reel, int index) {
    final isSelected = _selectedIndex == index;

    return GestureDetector(
      onTap: () => _onStoryTap(index),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: _storySize,
            height: _storySize,
            transform: Matrix4.identity()..scale(isSelected ? 1.1 : 1.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.amber : Colors.grey[400]!,
                width: isSelected ? 3 : 2,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ]
                  : null,
            ),
            child: ClipOval(
              child: CachedNetworkImage(
                imageUrl: reel.userAvatarUrl,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.person, color: Colors.grey),
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.person, color: Colors.grey),
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
          SizedBox(
            width: _storySize + 10,
            child: Text(
              reel.username,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Include all stories, including current user's stories
    final filteredStories = widget.storyReels;

    return Column(
      children: [
        // Story count indicator for large datasets
        if (filteredStories.length > 20)
          Container(
            margin: const EdgeInsets.only(bottom: 4),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.amber.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.amber.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              '${filteredStories.length} stories',
              style: TextStyle(
                color: Colors.amber[700],
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

        // Horizontal story carousel
        Container(
          height: 100,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: filteredStories.length + 1, // +1 for create button
            // Performance optimizations for large datasets
            cacheExtent: 1000, // Cache more items for smooth scrolling
            itemBuilder: (context, index) {
              if (index == 0) {
                // First item is the create story button
                return Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: _buildCreateStoryButton(),
                );
              }

              // Story items
              final storyIndex = index - 1;
              final reel = filteredStories[storyIndex];

              return Padding(
                padding: const EdgeInsets.only(right: 12),
                child: _buildStoryItem(reel, storyIndex),
              );
            },
          ),
        ),
      ],
    );
  }
}
