// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verification_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VerificationRequest {

 String get id; String get userId; String get username; String get userEmail; VerificationTier get requestedTier; VerificationStatus get status; DateTime get submittedAt; DateTime? get reviewedAt; String? get reviewedBy;// Admin ID who reviewed
 String? get rejectionReason;// Supporting documents and information
 String get fullName; String get profession; String? get company; String? get website; String? get linkedinUrl; String? get twitterUrl; String? get instagramUrl; List<String>? get supportingDocuments;// URLs to uploaded documents
 String? get additionalInfo;// For billionaire verification
 String? get netWorthEvidence; String? get businessOwnership; String? get publicRecognition;// For celebrity verification
 String? get publicProfile; String? get mediaPresence; String? get followerCount;// Admin notes
 String? get adminNotes; Map<String, dynamic>? get metadata;
/// Create a copy of VerificationRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VerificationRequestCopyWith<VerificationRequest> get copyWith => _$VerificationRequestCopyWithImpl<VerificationRequest>(this as VerificationRequest, _$identity);

  /// Serializes this VerificationRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VerificationRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.requestedTier, requestedTier) || other.requestedTier == requestedTier)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.profession, profession) || other.profession == profession)&&(identical(other.company, company) || other.company == company)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.twitterUrl, twitterUrl) || other.twitterUrl == twitterUrl)&&(identical(other.instagramUrl, instagramUrl) || other.instagramUrl == instagramUrl)&&const DeepCollectionEquality().equals(other.supportingDocuments, supportingDocuments)&&(identical(other.additionalInfo, additionalInfo) || other.additionalInfo == additionalInfo)&&(identical(other.netWorthEvidence, netWorthEvidence) || other.netWorthEvidence == netWorthEvidence)&&(identical(other.businessOwnership, businessOwnership) || other.businessOwnership == businessOwnership)&&(identical(other.publicRecognition, publicRecognition) || other.publicRecognition == publicRecognition)&&(identical(other.publicProfile, publicProfile) || other.publicProfile == publicProfile)&&(identical(other.mediaPresence, mediaPresence) || other.mediaPresence == mediaPresence)&&(identical(other.followerCount, followerCount) || other.followerCount == followerCount)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,username,userEmail,requestedTier,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,fullName,profession,company,website,linkedinUrl,twitterUrl,instagramUrl,const DeepCollectionEquality().hash(supportingDocuments),additionalInfo,netWorthEvidence,businessOwnership,publicRecognition,publicProfile,mediaPresence,followerCount,adminNotes,const DeepCollectionEquality().hash(metadata)]);

@override
String toString() {
  return 'VerificationRequest(id: $id, userId: $userId, username: $username, userEmail: $userEmail, requestedTier: $requestedTier, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, fullName: $fullName, profession: $profession, company: $company, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, supportingDocuments: $supportingDocuments, additionalInfo: $additionalInfo, netWorthEvidence: $netWorthEvidence, businessOwnership: $businessOwnership, publicRecognition: $publicRecognition, publicProfile: $publicProfile, mediaPresence: $mediaPresence, followerCount: $followerCount, adminNotes: $adminNotes, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $VerificationRequestCopyWith<$Res>  {
  factory $VerificationRequestCopyWith(VerificationRequest value, $Res Function(VerificationRequest) _then) = _$VerificationRequestCopyWithImpl;
@useResult
$Res call({
 String id, String userId, String username, String userEmail, VerificationTier requestedTier, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, String fullName, String profession, String? company, String? website, String? linkedinUrl, String? twitterUrl, String? instagramUrl, List<String>? supportingDocuments, String? additionalInfo, String? netWorthEvidence, String? businessOwnership, String? publicRecognition, String? publicProfile, String? mediaPresence, String? followerCount, String? adminNotes, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$VerificationRequestCopyWithImpl<$Res>
    implements $VerificationRequestCopyWith<$Res> {
  _$VerificationRequestCopyWithImpl(this._self, this._then);

  final VerificationRequest _self;
  final $Res Function(VerificationRequest) _then;

/// Create a copy of VerificationRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userEmail = null,Object? requestedTier = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? fullName = null,Object? profession = null,Object? company = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? twitterUrl = freezed,Object? instagramUrl = freezed,Object? supportingDocuments = freezed,Object? additionalInfo = freezed,Object? netWorthEvidence = freezed,Object? businessOwnership = freezed,Object? publicRecognition = freezed,Object? publicProfile = freezed,Object? mediaPresence = freezed,Object? followerCount = freezed,Object? adminNotes = freezed,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,requestedTier: null == requestedTier ? _self.requestedTier : requestedTier // ignore: cast_nullable_to_non_nullable
as VerificationTier,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,profession: null == profession ? _self.profession : profession // ignore: cast_nullable_to_non_nullable
as String,company: freezed == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,twitterUrl: freezed == twitterUrl ? _self.twitterUrl : twitterUrl // ignore: cast_nullable_to_non_nullable
as String?,instagramUrl: freezed == instagramUrl ? _self.instagramUrl : instagramUrl // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self.supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as String?,netWorthEvidence: freezed == netWorthEvidence ? _self.netWorthEvidence : netWorthEvidence // ignore: cast_nullable_to_non_nullable
as String?,businessOwnership: freezed == businessOwnership ? _self.businessOwnership : businessOwnership // ignore: cast_nullable_to_non_nullable
as String?,publicRecognition: freezed == publicRecognition ? _self.publicRecognition : publicRecognition // ignore: cast_nullable_to_non_nullable
as String?,publicProfile: freezed == publicProfile ? _self.publicProfile : publicProfile // ignore: cast_nullable_to_non_nullable
as String?,mediaPresence: freezed == mediaPresence ? _self.mediaPresence : mediaPresence // ignore: cast_nullable_to_non_nullable
as String?,followerCount: freezed == followerCount ? _self.followerCount : followerCount // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// Adds pattern-matching-related methods to [VerificationRequest].
extension VerificationRequestPatterns on VerificationRequest {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VerificationRequest value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VerificationRequest() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VerificationRequest value)  $default,){
final _that = this;
switch (_that) {
case _VerificationRequest():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VerificationRequest value)?  $default,){
final _that = this;
switch (_that) {
case _VerificationRequest() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userEmail,  VerificationTier requestedTier,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String fullName,  String profession,  String? company,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  List<String>? supportingDocuments,  String? additionalInfo,  String? netWorthEvidence,  String? businessOwnership,  String? publicRecognition,  String? publicProfile,  String? mediaPresence,  String? followerCount,  String? adminNotes,  Map<String, dynamic>? metadata)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VerificationRequest() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userEmail,_that.requestedTier,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.fullName,_that.profession,_that.company,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.supportingDocuments,_that.additionalInfo,_that.netWorthEvidence,_that.businessOwnership,_that.publicRecognition,_that.publicProfile,_that.mediaPresence,_that.followerCount,_that.adminNotes,_that.metadata);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String userId,  String username,  String userEmail,  VerificationTier requestedTier,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String fullName,  String profession,  String? company,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  List<String>? supportingDocuments,  String? additionalInfo,  String? netWorthEvidence,  String? businessOwnership,  String? publicRecognition,  String? publicProfile,  String? mediaPresence,  String? followerCount,  String? adminNotes,  Map<String, dynamic>? metadata)  $default,) {final _that = this;
switch (_that) {
case _VerificationRequest():
return $default(_that.id,_that.userId,_that.username,_that.userEmail,_that.requestedTier,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.fullName,_that.profession,_that.company,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.supportingDocuments,_that.additionalInfo,_that.netWorthEvidence,_that.businessOwnership,_that.publicRecognition,_that.publicProfile,_that.mediaPresence,_that.followerCount,_that.adminNotes,_that.metadata);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String userId,  String username,  String userEmail,  VerificationTier requestedTier,  VerificationStatus status,  DateTime submittedAt,  DateTime? reviewedAt,  String? reviewedBy,  String? rejectionReason,  String fullName,  String profession,  String? company,  String? website,  String? linkedinUrl,  String? twitterUrl,  String? instagramUrl,  List<String>? supportingDocuments,  String? additionalInfo,  String? netWorthEvidence,  String? businessOwnership,  String? publicRecognition,  String? publicProfile,  String? mediaPresence,  String? followerCount,  String? adminNotes,  Map<String, dynamic>? metadata)?  $default,) {final _that = this;
switch (_that) {
case _VerificationRequest() when $default != null:
return $default(_that.id,_that.userId,_that.username,_that.userEmail,_that.requestedTier,_that.status,_that.submittedAt,_that.reviewedAt,_that.reviewedBy,_that.rejectionReason,_that.fullName,_that.profession,_that.company,_that.website,_that.linkedinUrl,_that.twitterUrl,_that.instagramUrl,_that.supportingDocuments,_that.additionalInfo,_that.netWorthEvidence,_that.businessOwnership,_that.publicRecognition,_that.publicProfile,_that.mediaPresence,_that.followerCount,_that.adminNotes,_that.metadata);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VerificationRequest implements VerificationRequest {
  const _VerificationRequest({required this.id, required this.userId, required this.username, required this.userEmail, required this.requestedTier, required this.status, required this.submittedAt, this.reviewedAt, this.reviewedBy, this.rejectionReason, required this.fullName, required this.profession, this.company, this.website, this.linkedinUrl, this.twitterUrl, this.instagramUrl, final  List<String>? supportingDocuments, this.additionalInfo, this.netWorthEvidence, this.businessOwnership, this.publicRecognition, this.publicProfile, this.mediaPresence, this.followerCount, this.adminNotes, final  Map<String, dynamic>? metadata}): _supportingDocuments = supportingDocuments,_metadata = metadata;
  factory _VerificationRequest.fromJson(Map<String, dynamic> json) => _$VerificationRequestFromJson(json);

@override final  String id;
@override final  String userId;
@override final  String username;
@override final  String userEmail;
@override final  VerificationTier requestedTier;
@override final  VerificationStatus status;
@override final  DateTime submittedAt;
@override final  DateTime? reviewedAt;
@override final  String? reviewedBy;
// Admin ID who reviewed
@override final  String? rejectionReason;
// Supporting documents and information
@override final  String fullName;
@override final  String profession;
@override final  String? company;
@override final  String? website;
@override final  String? linkedinUrl;
@override final  String? twitterUrl;
@override final  String? instagramUrl;
 final  List<String>? _supportingDocuments;
@override List<String>? get supportingDocuments {
  final value = _supportingDocuments;
  if (value == null) return null;
  if (_supportingDocuments is EqualUnmodifiableListView) return _supportingDocuments;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

// URLs to uploaded documents
@override final  String? additionalInfo;
// For billionaire verification
@override final  String? netWorthEvidence;
@override final  String? businessOwnership;
@override final  String? publicRecognition;
// For celebrity verification
@override final  String? publicProfile;
@override final  String? mediaPresence;
@override final  String? followerCount;
// Admin notes
@override final  String? adminNotes;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of VerificationRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VerificationRequestCopyWith<_VerificationRequest> get copyWith => __$VerificationRequestCopyWithImpl<_VerificationRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VerificationRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VerificationRequest&&(identical(other.id, id) || other.id == id)&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.username, username) || other.username == username)&&(identical(other.userEmail, userEmail) || other.userEmail == userEmail)&&(identical(other.requestedTier, requestedTier) || other.requestedTier == requestedTier)&&(identical(other.status, status) || other.status == status)&&(identical(other.submittedAt, submittedAt) || other.submittedAt == submittedAt)&&(identical(other.reviewedAt, reviewedAt) || other.reviewedAt == reviewedAt)&&(identical(other.reviewedBy, reviewedBy) || other.reviewedBy == reviewedBy)&&(identical(other.rejectionReason, rejectionReason) || other.rejectionReason == rejectionReason)&&(identical(other.fullName, fullName) || other.fullName == fullName)&&(identical(other.profession, profession) || other.profession == profession)&&(identical(other.company, company) || other.company == company)&&(identical(other.website, website) || other.website == website)&&(identical(other.linkedinUrl, linkedinUrl) || other.linkedinUrl == linkedinUrl)&&(identical(other.twitterUrl, twitterUrl) || other.twitterUrl == twitterUrl)&&(identical(other.instagramUrl, instagramUrl) || other.instagramUrl == instagramUrl)&&const DeepCollectionEquality().equals(other._supportingDocuments, _supportingDocuments)&&(identical(other.additionalInfo, additionalInfo) || other.additionalInfo == additionalInfo)&&(identical(other.netWorthEvidence, netWorthEvidence) || other.netWorthEvidence == netWorthEvidence)&&(identical(other.businessOwnership, businessOwnership) || other.businessOwnership == businessOwnership)&&(identical(other.publicRecognition, publicRecognition) || other.publicRecognition == publicRecognition)&&(identical(other.publicProfile, publicProfile) || other.publicProfile == publicProfile)&&(identical(other.mediaPresence, mediaPresence) || other.mediaPresence == mediaPresence)&&(identical(other.followerCount, followerCount) || other.followerCount == followerCount)&&(identical(other.adminNotes, adminNotes) || other.adminNotes == adminNotes)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,id,userId,username,userEmail,requestedTier,status,submittedAt,reviewedAt,reviewedBy,rejectionReason,fullName,profession,company,website,linkedinUrl,twitterUrl,instagramUrl,const DeepCollectionEquality().hash(_supportingDocuments),additionalInfo,netWorthEvidence,businessOwnership,publicRecognition,publicProfile,mediaPresence,followerCount,adminNotes,const DeepCollectionEquality().hash(_metadata)]);

@override
String toString() {
  return 'VerificationRequest(id: $id, userId: $userId, username: $username, userEmail: $userEmail, requestedTier: $requestedTier, status: $status, submittedAt: $submittedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy, rejectionReason: $rejectionReason, fullName: $fullName, profession: $profession, company: $company, website: $website, linkedinUrl: $linkedinUrl, twitterUrl: $twitterUrl, instagramUrl: $instagramUrl, supportingDocuments: $supportingDocuments, additionalInfo: $additionalInfo, netWorthEvidence: $netWorthEvidence, businessOwnership: $businessOwnership, publicRecognition: $publicRecognition, publicProfile: $publicProfile, mediaPresence: $mediaPresence, followerCount: $followerCount, adminNotes: $adminNotes, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$VerificationRequestCopyWith<$Res> implements $VerificationRequestCopyWith<$Res> {
  factory _$VerificationRequestCopyWith(_VerificationRequest value, $Res Function(_VerificationRequest) _then) = __$VerificationRequestCopyWithImpl;
@override @useResult
$Res call({
 String id, String userId, String username, String userEmail, VerificationTier requestedTier, VerificationStatus status, DateTime submittedAt, DateTime? reviewedAt, String? reviewedBy, String? rejectionReason, String fullName, String profession, String? company, String? website, String? linkedinUrl, String? twitterUrl, String? instagramUrl, List<String>? supportingDocuments, String? additionalInfo, String? netWorthEvidence, String? businessOwnership, String? publicRecognition, String? publicProfile, String? mediaPresence, String? followerCount, String? adminNotes, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$VerificationRequestCopyWithImpl<$Res>
    implements _$VerificationRequestCopyWith<$Res> {
  __$VerificationRequestCopyWithImpl(this._self, this._then);

  final _VerificationRequest _self;
  final $Res Function(_VerificationRequest) _then;

/// Create a copy of VerificationRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? userId = null,Object? username = null,Object? userEmail = null,Object? requestedTier = null,Object? status = null,Object? submittedAt = null,Object? reviewedAt = freezed,Object? reviewedBy = freezed,Object? rejectionReason = freezed,Object? fullName = null,Object? profession = null,Object? company = freezed,Object? website = freezed,Object? linkedinUrl = freezed,Object? twitterUrl = freezed,Object? instagramUrl = freezed,Object? supportingDocuments = freezed,Object? additionalInfo = freezed,Object? netWorthEvidence = freezed,Object? businessOwnership = freezed,Object? publicRecognition = freezed,Object? publicProfile = freezed,Object? mediaPresence = freezed,Object? followerCount = freezed,Object? adminNotes = freezed,Object? metadata = freezed,}) {
  return _then(_VerificationRequest(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,username: null == username ? _self.username : username // ignore: cast_nullable_to_non_nullable
as String,userEmail: null == userEmail ? _self.userEmail : userEmail // ignore: cast_nullable_to_non_nullable
as String,requestedTier: null == requestedTier ? _self.requestedTier : requestedTier // ignore: cast_nullable_to_non_nullable
as VerificationTier,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as VerificationStatus,submittedAt: null == submittedAt ? _self.submittedAt : submittedAt // ignore: cast_nullable_to_non_nullable
as DateTime,reviewedAt: freezed == reviewedAt ? _self.reviewedAt : reviewedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,reviewedBy: freezed == reviewedBy ? _self.reviewedBy : reviewedBy // ignore: cast_nullable_to_non_nullable
as String?,rejectionReason: freezed == rejectionReason ? _self.rejectionReason : rejectionReason // ignore: cast_nullable_to_non_nullable
as String?,fullName: null == fullName ? _self.fullName : fullName // ignore: cast_nullable_to_non_nullable
as String,profession: null == profession ? _self.profession : profession // ignore: cast_nullable_to_non_nullable
as String,company: freezed == company ? _self.company : company // ignore: cast_nullable_to_non_nullable
as String?,website: freezed == website ? _self.website : website // ignore: cast_nullable_to_non_nullable
as String?,linkedinUrl: freezed == linkedinUrl ? _self.linkedinUrl : linkedinUrl // ignore: cast_nullable_to_non_nullable
as String?,twitterUrl: freezed == twitterUrl ? _self.twitterUrl : twitterUrl // ignore: cast_nullable_to_non_nullable
as String?,instagramUrl: freezed == instagramUrl ? _self.instagramUrl : instagramUrl // ignore: cast_nullable_to_non_nullable
as String?,supportingDocuments: freezed == supportingDocuments ? _self._supportingDocuments : supportingDocuments // ignore: cast_nullable_to_non_nullable
as List<String>?,additionalInfo: freezed == additionalInfo ? _self.additionalInfo : additionalInfo // ignore: cast_nullable_to_non_nullable
as String?,netWorthEvidence: freezed == netWorthEvidence ? _self.netWorthEvidence : netWorthEvidence // ignore: cast_nullable_to_non_nullable
as String?,businessOwnership: freezed == businessOwnership ? _self.businessOwnership : businessOwnership // ignore: cast_nullable_to_non_nullable
as String?,publicRecognition: freezed == publicRecognition ? _self.publicRecognition : publicRecognition // ignore: cast_nullable_to_non_nullable
as String?,publicProfile: freezed == publicProfile ? _self.publicProfile : publicProfile // ignore: cast_nullable_to_non_nullable
as String?,mediaPresence: freezed == mediaPresence ? _self.mediaPresence : mediaPresence // ignore: cast_nullable_to_non_nullable
as String?,followerCount: freezed == followerCount ? _self.followerCount : followerCount // ignore: cast_nullable_to_non_nullable
as String?,adminNotes: freezed == adminNotes ? _self.adminNotes : adminNotes // ignore: cast_nullable_to_non_nullable
as String?,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$UserVerification {

 String get userId; bool get isVerified; bool get isBillionaire; bool get isCelebrity; VerificationTier? get verificationTier; DateTime? get verifiedAt; String? get verifiedBy;// Admin ID who verified
 String? get verificationRequestId;// Badge customization
 String? get customBadgeColor; String? get customBadgeIcon;// Verification metadata
 Map<String, dynamic>? get verificationMetadata;// Privileges
 bool get hasBlueCheckmark; bool get hasGoldStar; bool get hasGoldB; bool get canCreateVerifiedContent; bool get priorityInSearch; bool get priorityInFeed;
/// Create a copy of UserVerification
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserVerificationCopyWith<UserVerification> get copyWith => _$UserVerificationCopyWithImpl<UserVerification>(this as UserVerification, _$identity);

  /// Serializes this UserVerification to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserVerification&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isBillionaire, isBillionaire) || other.isBillionaire == isBillionaire)&&(identical(other.isCelebrity, isCelebrity) || other.isCelebrity == isCelebrity)&&(identical(other.verificationTier, verificationTier) || other.verificationTier == verificationTier)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&(identical(other.verificationRequestId, verificationRequestId) || other.verificationRequestId == verificationRequestId)&&(identical(other.customBadgeColor, customBadgeColor) || other.customBadgeColor == customBadgeColor)&&(identical(other.customBadgeIcon, customBadgeIcon) || other.customBadgeIcon == customBadgeIcon)&&const DeepCollectionEquality().equals(other.verificationMetadata, verificationMetadata)&&(identical(other.hasBlueCheckmark, hasBlueCheckmark) || other.hasBlueCheckmark == hasBlueCheckmark)&&(identical(other.hasGoldStar, hasGoldStar) || other.hasGoldStar == hasGoldStar)&&(identical(other.hasGoldB, hasGoldB) || other.hasGoldB == hasGoldB)&&(identical(other.canCreateVerifiedContent, canCreateVerifiedContent) || other.canCreateVerifiedContent == canCreateVerifiedContent)&&(identical(other.priorityInSearch, priorityInSearch) || other.priorityInSearch == priorityInSearch)&&(identical(other.priorityInFeed, priorityInFeed) || other.priorityInFeed == priorityInFeed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,isVerified,isBillionaire,isCelebrity,verificationTier,verifiedAt,verifiedBy,verificationRequestId,customBadgeColor,customBadgeIcon,const DeepCollectionEquality().hash(verificationMetadata),hasBlueCheckmark,hasGoldStar,hasGoldB,canCreateVerifiedContent,priorityInSearch,priorityInFeed);

@override
String toString() {
  return 'UserVerification(userId: $userId, isVerified: $isVerified, isBillionaire: $isBillionaire, isCelebrity: $isCelebrity, verificationTier: $verificationTier, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, verificationRequestId: $verificationRequestId, customBadgeColor: $customBadgeColor, customBadgeIcon: $customBadgeIcon, verificationMetadata: $verificationMetadata, hasBlueCheckmark: $hasBlueCheckmark, hasGoldStar: $hasGoldStar, hasGoldB: $hasGoldB, canCreateVerifiedContent: $canCreateVerifiedContent, priorityInSearch: $priorityInSearch, priorityInFeed: $priorityInFeed)';
}


}

/// @nodoc
abstract mixin class $UserVerificationCopyWith<$Res>  {
  factory $UserVerificationCopyWith(UserVerification value, $Res Function(UserVerification) _then) = _$UserVerificationCopyWithImpl;
@useResult
$Res call({
 String userId, bool isVerified, bool isBillionaire, bool isCelebrity, VerificationTier? verificationTier, DateTime? verifiedAt, String? verifiedBy, String? verificationRequestId, String? customBadgeColor, String? customBadgeIcon, Map<String, dynamic>? verificationMetadata, bool hasBlueCheckmark, bool hasGoldStar, bool hasGoldB, bool canCreateVerifiedContent, bool priorityInSearch, bool priorityInFeed
});




}
/// @nodoc
class _$UserVerificationCopyWithImpl<$Res>
    implements $UserVerificationCopyWith<$Res> {
  _$UserVerificationCopyWithImpl(this._self, this._then);

  final UserVerification _self;
  final $Res Function(UserVerification) _then;

/// Create a copy of UserVerification
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? userId = null,Object? isVerified = null,Object? isBillionaire = null,Object? isCelebrity = null,Object? verificationTier = freezed,Object? verifiedAt = freezed,Object? verifiedBy = freezed,Object? verificationRequestId = freezed,Object? customBadgeColor = freezed,Object? customBadgeIcon = freezed,Object? verificationMetadata = freezed,Object? hasBlueCheckmark = null,Object? hasGoldStar = null,Object? hasGoldB = null,Object? canCreateVerifiedContent = null,Object? priorityInSearch = null,Object? priorityInFeed = null,}) {
  return _then(_self.copyWith(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isBillionaire: null == isBillionaire ? _self.isBillionaire : isBillionaire // ignore: cast_nullable_to_non_nullable
as bool,isCelebrity: null == isCelebrity ? _self.isCelebrity : isCelebrity // ignore: cast_nullable_to_non_nullable
as bool,verificationTier: freezed == verificationTier ? _self.verificationTier : verificationTier // ignore: cast_nullable_to_non_nullable
as VerificationTier?,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,verificationRequestId: freezed == verificationRequestId ? _self.verificationRequestId : verificationRequestId // ignore: cast_nullable_to_non_nullable
as String?,customBadgeColor: freezed == customBadgeColor ? _self.customBadgeColor : customBadgeColor // ignore: cast_nullable_to_non_nullable
as String?,customBadgeIcon: freezed == customBadgeIcon ? _self.customBadgeIcon : customBadgeIcon // ignore: cast_nullable_to_non_nullable
as String?,verificationMetadata: freezed == verificationMetadata ? _self.verificationMetadata : verificationMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,hasBlueCheckmark: null == hasBlueCheckmark ? _self.hasBlueCheckmark : hasBlueCheckmark // ignore: cast_nullable_to_non_nullable
as bool,hasGoldStar: null == hasGoldStar ? _self.hasGoldStar : hasGoldStar // ignore: cast_nullable_to_non_nullable
as bool,hasGoldB: null == hasGoldB ? _self.hasGoldB : hasGoldB // ignore: cast_nullable_to_non_nullable
as bool,canCreateVerifiedContent: null == canCreateVerifiedContent ? _self.canCreateVerifiedContent : canCreateVerifiedContent // ignore: cast_nullable_to_non_nullable
as bool,priorityInSearch: null == priorityInSearch ? _self.priorityInSearch : priorityInSearch // ignore: cast_nullable_to_non_nullable
as bool,priorityInFeed: null == priorityInFeed ? _self.priorityInFeed : priorityInFeed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [UserVerification].
extension UserVerificationPatterns on UserVerification {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _UserVerification value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _UserVerification() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _UserVerification value)  $default,){
final _that = this;
switch (_that) {
case _UserVerification():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _UserVerification value)?  $default,){
final _that = this;
switch (_that) {
case _UserVerification() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String userId,  bool isVerified,  bool isBillionaire,  bool isCelebrity,  VerificationTier? verificationTier,  DateTime? verifiedAt,  String? verifiedBy,  String? verificationRequestId,  String? customBadgeColor,  String? customBadgeIcon,  Map<String, dynamic>? verificationMetadata,  bool hasBlueCheckmark,  bool hasGoldStar,  bool hasGoldB,  bool canCreateVerifiedContent,  bool priorityInSearch,  bool priorityInFeed)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _UserVerification() when $default != null:
return $default(_that.userId,_that.isVerified,_that.isBillionaire,_that.isCelebrity,_that.verificationTier,_that.verifiedAt,_that.verifiedBy,_that.verificationRequestId,_that.customBadgeColor,_that.customBadgeIcon,_that.verificationMetadata,_that.hasBlueCheckmark,_that.hasGoldStar,_that.hasGoldB,_that.canCreateVerifiedContent,_that.priorityInSearch,_that.priorityInFeed);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String userId,  bool isVerified,  bool isBillionaire,  bool isCelebrity,  VerificationTier? verificationTier,  DateTime? verifiedAt,  String? verifiedBy,  String? verificationRequestId,  String? customBadgeColor,  String? customBadgeIcon,  Map<String, dynamic>? verificationMetadata,  bool hasBlueCheckmark,  bool hasGoldStar,  bool hasGoldB,  bool canCreateVerifiedContent,  bool priorityInSearch,  bool priorityInFeed)  $default,) {final _that = this;
switch (_that) {
case _UserVerification():
return $default(_that.userId,_that.isVerified,_that.isBillionaire,_that.isCelebrity,_that.verificationTier,_that.verifiedAt,_that.verifiedBy,_that.verificationRequestId,_that.customBadgeColor,_that.customBadgeIcon,_that.verificationMetadata,_that.hasBlueCheckmark,_that.hasGoldStar,_that.hasGoldB,_that.canCreateVerifiedContent,_that.priorityInSearch,_that.priorityInFeed);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String userId,  bool isVerified,  bool isBillionaire,  bool isCelebrity,  VerificationTier? verificationTier,  DateTime? verifiedAt,  String? verifiedBy,  String? verificationRequestId,  String? customBadgeColor,  String? customBadgeIcon,  Map<String, dynamic>? verificationMetadata,  bool hasBlueCheckmark,  bool hasGoldStar,  bool hasGoldB,  bool canCreateVerifiedContent,  bool priorityInSearch,  bool priorityInFeed)?  $default,) {final _that = this;
switch (_that) {
case _UserVerification() when $default != null:
return $default(_that.userId,_that.isVerified,_that.isBillionaire,_that.isCelebrity,_that.verificationTier,_that.verifiedAt,_that.verifiedBy,_that.verificationRequestId,_that.customBadgeColor,_that.customBadgeIcon,_that.verificationMetadata,_that.hasBlueCheckmark,_that.hasGoldStar,_that.hasGoldB,_that.canCreateVerifiedContent,_that.priorityInSearch,_that.priorityInFeed);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _UserVerification implements UserVerification {
  const _UserVerification({required this.userId, this.isVerified = false, this.isBillionaire = false, this.isCelebrity = false, this.verificationTier, this.verifiedAt, this.verifiedBy, this.verificationRequestId, this.customBadgeColor, this.customBadgeIcon, final  Map<String, dynamic>? verificationMetadata, this.hasBlueCheckmark = false, this.hasGoldStar = false, this.hasGoldB = false, this.canCreateVerifiedContent = false, this.priorityInSearch = false, this.priorityInFeed = false}): _verificationMetadata = verificationMetadata;
  factory _UserVerification.fromJson(Map<String, dynamic> json) => _$UserVerificationFromJson(json);

@override final  String userId;
@override@JsonKey() final  bool isVerified;
@override@JsonKey() final  bool isBillionaire;
@override@JsonKey() final  bool isCelebrity;
@override final  VerificationTier? verificationTier;
@override final  DateTime? verifiedAt;
@override final  String? verifiedBy;
// Admin ID who verified
@override final  String? verificationRequestId;
// Badge customization
@override final  String? customBadgeColor;
@override final  String? customBadgeIcon;
// Verification metadata
 final  Map<String, dynamic>? _verificationMetadata;
// Verification metadata
@override Map<String, dynamic>? get verificationMetadata {
  final value = _verificationMetadata;
  if (value == null) return null;
  if (_verificationMetadata is EqualUnmodifiableMapView) return _verificationMetadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}

// Privileges
@override@JsonKey() final  bool hasBlueCheckmark;
@override@JsonKey() final  bool hasGoldStar;
@override@JsonKey() final  bool hasGoldB;
@override@JsonKey() final  bool canCreateVerifiedContent;
@override@JsonKey() final  bool priorityInSearch;
@override@JsonKey() final  bool priorityInFeed;

/// Create a copy of UserVerification
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserVerificationCopyWith<_UserVerification> get copyWith => __$UserVerificationCopyWithImpl<_UserVerification>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserVerificationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserVerification&&(identical(other.userId, userId) || other.userId == userId)&&(identical(other.isVerified, isVerified) || other.isVerified == isVerified)&&(identical(other.isBillionaire, isBillionaire) || other.isBillionaire == isBillionaire)&&(identical(other.isCelebrity, isCelebrity) || other.isCelebrity == isCelebrity)&&(identical(other.verificationTier, verificationTier) || other.verificationTier == verificationTier)&&(identical(other.verifiedAt, verifiedAt) || other.verifiedAt == verifiedAt)&&(identical(other.verifiedBy, verifiedBy) || other.verifiedBy == verifiedBy)&&(identical(other.verificationRequestId, verificationRequestId) || other.verificationRequestId == verificationRequestId)&&(identical(other.customBadgeColor, customBadgeColor) || other.customBadgeColor == customBadgeColor)&&(identical(other.customBadgeIcon, customBadgeIcon) || other.customBadgeIcon == customBadgeIcon)&&const DeepCollectionEquality().equals(other._verificationMetadata, _verificationMetadata)&&(identical(other.hasBlueCheckmark, hasBlueCheckmark) || other.hasBlueCheckmark == hasBlueCheckmark)&&(identical(other.hasGoldStar, hasGoldStar) || other.hasGoldStar == hasGoldStar)&&(identical(other.hasGoldB, hasGoldB) || other.hasGoldB == hasGoldB)&&(identical(other.canCreateVerifiedContent, canCreateVerifiedContent) || other.canCreateVerifiedContent == canCreateVerifiedContent)&&(identical(other.priorityInSearch, priorityInSearch) || other.priorityInSearch == priorityInSearch)&&(identical(other.priorityInFeed, priorityInFeed) || other.priorityInFeed == priorityInFeed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,userId,isVerified,isBillionaire,isCelebrity,verificationTier,verifiedAt,verifiedBy,verificationRequestId,customBadgeColor,customBadgeIcon,const DeepCollectionEquality().hash(_verificationMetadata),hasBlueCheckmark,hasGoldStar,hasGoldB,canCreateVerifiedContent,priorityInSearch,priorityInFeed);

@override
String toString() {
  return 'UserVerification(userId: $userId, isVerified: $isVerified, isBillionaire: $isBillionaire, isCelebrity: $isCelebrity, verificationTier: $verificationTier, verifiedAt: $verifiedAt, verifiedBy: $verifiedBy, verificationRequestId: $verificationRequestId, customBadgeColor: $customBadgeColor, customBadgeIcon: $customBadgeIcon, verificationMetadata: $verificationMetadata, hasBlueCheckmark: $hasBlueCheckmark, hasGoldStar: $hasGoldStar, hasGoldB: $hasGoldB, canCreateVerifiedContent: $canCreateVerifiedContent, priorityInSearch: $priorityInSearch, priorityInFeed: $priorityInFeed)';
}


}

/// @nodoc
abstract mixin class _$UserVerificationCopyWith<$Res> implements $UserVerificationCopyWith<$Res> {
  factory _$UserVerificationCopyWith(_UserVerification value, $Res Function(_UserVerification) _then) = __$UserVerificationCopyWithImpl;
@override @useResult
$Res call({
 String userId, bool isVerified, bool isBillionaire, bool isCelebrity, VerificationTier? verificationTier, DateTime? verifiedAt, String? verifiedBy, String? verificationRequestId, String? customBadgeColor, String? customBadgeIcon, Map<String, dynamic>? verificationMetadata, bool hasBlueCheckmark, bool hasGoldStar, bool hasGoldB, bool canCreateVerifiedContent, bool priorityInSearch, bool priorityInFeed
});




}
/// @nodoc
class __$UserVerificationCopyWithImpl<$Res>
    implements _$UserVerificationCopyWith<$Res> {
  __$UserVerificationCopyWithImpl(this._self, this._then);

  final _UserVerification _self;
  final $Res Function(_UserVerification) _then;

/// Create a copy of UserVerification
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? userId = null,Object? isVerified = null,Object? isBillionaire = null,Object? isCelebrity = null,Object? verificationTier = freezed,Object? verifiedAt = freezed,Object? verifiedBy = freezed,Object? verificationRequestId = freezed,Object? customBadgeColor = freezed,Object? customBadgeIcon = freezed,Object? verificationMetadata = freezed,Object? hasBlueCheckmark = null,Object? hasGoldStar = null,Object? hasGoldB = null,Object? canCreateVerifiedContent = null,Object? priorityInSearch = null,Object? priorityInFeed = null,}) {
  return _then(_UserVerification(
userId: null == userId ? _self.userId : userId // ignore: cast_nullable_to_non_nullable
as String,isVerified: null == isVerified ? _self.isVerified : isVerified // ignore: cast_nullable_to_non_nullable
as bool,isBillionaire: null == isBillionaire ? _self.isBillionaire : isBillionaire // ignore: cast_nullable_to_non_nullable
as bool,isCelebrity: null == isCelebrity ? _self.isCelebrity : isCelebrity // ignore: cast_nullable_to_non_nullable
as bool,verificationTier: freezed == verificationTier ? _self.verificationTier : verificationTier // ignore: cast_nullable_to_non_nullable
as VerificationTier?,verifiedAt: freezed == verifiedAt ? _self.verifiedAt : verifiedAt // ignore: cast_nullable_to_non_nullable
as DateTime?,verifiedBy: freezed == verifiedBy ? _self.verifiedBy : verifiedBy // ignore: cast_nullable_to_non_nullable
as String?,verificationRequestId: freezed == verificationRequestId ? _self.verificationRequestId : verificationRequestId // ignore: cast_nullable_to_non_nullable
as String?,customBadgeColor: freezed == customBadgeColor ? _self.customBadgeColor : customBadgeColor // ignore: cast_nullable_to_non_nullable
as String?,customBadgeIcon: freezed == customBadgeIcon ? _self.customBadgeIcon : customBadgeIcon // ignore: cast_nullable_to_non_nullable
as String?,verificationMetadata: freezed == verificationMetadata ? _self._verificationMetadata : verificationMetadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,hasBlueCheckmark: null == hasBlueCheckmark ? _self.hasBlueCheckmark : hasBlueCheckmark // ignore: cast_nullable_to_non_nullable
as bool,hasGoldStar: null == hasGoldStar ? _self.hasGoldStar : hasGoldStar // ignore: cast_nullable_to_non_nullable
as bool,hasGoldB: null == hasGoldB ? _self.hasGoldB : hasGoldB // ignore: cast_nullable_to_non_nullable
as bool,canCreateVerifiedContent: null == canCreateVerifiedContent ? _self.canCreateVerifiedContent : canCreateVerifiedContent // ignore: cast_nullable_to_non_nullable
as bool,priorityInSearch: null == priorityInSearch ? _self.priorityInSearch : priorityInSearch // ignore: cast_nullable_to_non_nullable
as bool,priorityInFeed: null == priorityInFeed ? _self.priorityInFeed : priorityInFeed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$VerificationCriteria {

 VerificationTier get tier; String get title; String get description; List<String> get requirements; List<String> get documentationNeeded; String? get additionalNotes; bool get isActive;
/// Create a copy of VerificationCriteria
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VerificationCriteriaCopyWith<VerificationCriteria> get copyWith => _$VerificationCriteriaCopyWithImpl<VerificationCriteria>(this as VerificationCriteria, _$identity);

  /// Serializes this VerificationCriteria to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VerificationCriteria&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other.requirements, requirements)&&const DeepCollectionEquality().equals(other.documentationNeeded, documentationNeeded)&&(identical(other.additionalNotes, additionalNotes) || other.additionalNotes == additionalNotes)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tier,title,description,const DeepCollectionEquality().hash(requirements),const DeepCollectionEquality().hash(documentationNeeded),additionalNotes,isActive);

@override
String toString() {
  return 'VerificationCriteria(tier: $tier, title: $title, description: $description, requirements: $requirements, documentationNeeded: $documentationNeeded, additionalNotes: $additionalNotes, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class $VerificationCriteriaCopyWith<$Res>  {
  factory $VerificationCriteriaCopyWith(VerificationCriteria value, $Res Function(VerificationCriteria) _then) = _$VerificationCriteriaCopyWithImpl;
@useResult
$Res call({
 VerificationTier tier, String title, String description, List<String> requirements, List<String> documentationNeeded, String? additionalNotes, bool isActive
});




}
/// @nodoc
class _$VerificationCriteriaCopyWithImpl<$Res>
    implements $VerificationCriteriaCopyWith<$Res> {
  _$VerificationCriteriaCopyWithImpl(this._self, this._then);

  final VerificationCriteria _self;
  final $Res Function(VerificationCriteria) _then;

/// Create a copy of VerificationCriteria
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tier = null,Object? title = null,Object? description = null,Object? requirements = null,Object? documentationNeeded = null,Object? additionalNotes = freezed,Object? isActive = null,}) {
  return _then(_self.copyWith(
tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as VerificationTier,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,requirements: null == requirements ? _self.requirements : requirements // ignore: cast_nullable_to_non_nullable
as List<String>,documentationNeeded: null == documentationNeeded ? _self.documentationNeeded : documentationNeeded // ignore: cast_nullable_to_non_nullable
as List<String>,additionalNotes: freezed == additionalNotes ? _self.additionalNotes : additionalNotes // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// Adds pattern-matching-related methods to [VerificationCriteria].
extension VerificationCriteriaPatterns on VerificationCriteria {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VerificationCriteria value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VerificationCriteria() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VerificationCriteria value)  $default,){
final _that = this;
switch (_that) {
case _VerificationCriteria():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VerificationCriteria value)?  $default,){
final _that = this;
switch (_that) {
case _VerificationCriteria() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( VerificationTier tier,  String title,  String description,  List<String> requirements,  List<String> documentationNeeded,  String? additionalNotes,  bool isActive)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VerificationCriteria() when $default != null:
return $default(_that.tier,_that.title,_that.description,_that.requirements,_that.documentationNeeded,_that.additionalNotes,_that.isActive);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( VerificationTier tier,  String title,  String description,  List<String> requirements,  List<String> documentationNeeded,  String? additionalNotes,  bool isActive)  $default,) {final _that = this;
switch (_that) {
case _VerificationCriteria():
return $default(_that.tier,_that.title,_that.description,_that.requirements,_that.documentationNeeded,_that.additionalNotes,_that.isActive);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( VerificationTier tier,  String title,  String description,  List<String> requirements,  List<String> documentationNeeded,  String? additionalNotes,  bool isActive)?  $default,) {final _that = this;
switch (_that) {
case _VerificationCriteria() when $default != null:
return $default(_that.tier,_that.title,_that.description,_that.requirements,_that.documentationNeeded,_that.additionalNotes,_that.isActive);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VerificationCriteria implements VerificationCriteria {
  const _VerificationCriteria({required this.tier, required this.title, required this.description, required final  List<String> requirements, required final  List<String> documentationNeeded, this.additionalNotes, this.isActive = false}): _requirements = requirements,_documentationNeeded = documentationNeeded;
  factory _VerificationCriteria.fromJson(Map<String, dynamic> json) => _$VerificationCriteriaFromJson(json);

@override final  VerificationTier tier;
@override final  String title;
@override final  String description;
 final  List<String> _requirements;
@override List<String> get requirements {
  if (_requirements is EqualUnmodifiableListView) return _requirements;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_requirements);
}

 final  List<String> _documentationNeeded;
@override List<String> get documentationNeeded {
  if (_documentationNeeded is EqualUnmodifiableListView) return _documentationNeeded;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_documentationNeeded);
}

@override final  String? additionalNotes;
@override@JsonKey() final  bool isActive;

/// Create a copy of VerificationCriteria
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VerificationCriteriaCopyWith<_VerificationCriteria> get copyWith => __$VerificationCriteriaCopyWithImpl<_VerificationCriteria>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VerificationCriteriaToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VerificationCriteria&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&const DeepCollectionEquality().equals(other._requirements, _requirements)&&const DeepCollectionEquality().equals(other._documentationNeeded, _documentationNeeded)&&(identical(other.additionalNotes, additionalNotes) || other.additionalNotes == additionalNotes)&&(identical(other.isActive, isActive) || other.isActive == isActive));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tier,title,description,const DeepCollectionEquality().hash(_requirements),const DeepCollectionEquality().hash(_documentationNeeded),additionalNotes,isActive);

@override
String toString() {
  return 'VerificationCriteria(tier: $tier, title: $title, description: $description, requirements: $requirements, documentationNeeded: $documentationNeeded, additionalNotes: $additionalNotes, isActive: $isActive)';
}


}

/// @nodoc
abstract mixin class _$VerificationCriteriaCopyWith<$Res> implements $VerificationCriteriaCopyWith<$Res> {
  factory _$VerificationCriteriaCopyWith(_VerificationCriteria value, $Res Function(_VerificationCriteria) _then) = __$VerificationCriteriaCopyWithImpl;
@override @useResult
$Res call({
 VerificationTier tier, String title, String description, List<String> requirements, List<String> documentationNeeded, String? additionalNotes, bool isActive
});




}
/// @nodoc
class __$VerificationCriteriaCopyWithImpl<$Res>
    implements _$VerificationCriteriaCopyWith<$Res> {
  __$VerificationCriteriaCopyWithImpl(this._self, this._then);

  final _VerificationCriteria _self;
  final $Res Function(_VerificationCriteria) _then;

/// Create a copy of VerificationCriteria
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tier = null,Object? title = null,Object? description = null,Object? requirements = null,Object? documentationNeeded = null,Object? additionalNotes = freezed,Object? isActive = null,}) {
  return _then(_VerificationCriteria(
tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as VerificationTier,title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,requirements: null == requirements ? _self._requirements : requirements // ignore: cast_nullable_to_non_nullable
as List<String>,documentationNeeded: null == documentationNeeded ? _self._documentationNeeded : documentationNeeded // ignore: cast_nullable_to_non_nullable
as List<String>,additionalNotes: freezed == additionalNotes ? _self.additionalNotes : additionalNotes // ignore: cast_nullable_to_non_nullable
as String?,isActive: null == isActive ? _self.isActive : isActive // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$VerificationBadgeConfig {

 VerificationTier get tier; String get iconName; String get color; String get displayName; String get description; double get smallSize; double get mediumSize; double get largeSize;
/// Create a copy of VerificationBadgeConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VerificationBadgeConfigCopyWith<VerificationBadgeConfig> get copyWith => _$VerificationBadgeConfigCopyWithImpl<VerificationBadgeConfig>(this as VerificationBadgeConfig, _$identity);

  /// Serializes this VerificationBadgeConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VerificationBadgeConfig&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.color, color) || other.color == color)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.description, description) || other.description == description)&&(identical(other.smallSize, smallSize) || other.smallSize == smallSize)&&(identical(other.mediumSize, mediumSize) || other.mediumSize == mediumSize)&&(identical(other.largeSize, largeSize) || other.largeSize == largeSize));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tier,iconName,color,displayName,description,smallSize,mediumSize,largeSize);

@override
String toString() {
  return 'VerificationBadgeConfig(tier: $tier, iconName: $iconName, color: $color, displayName: $displayName, description: $description, smallSize: $smallSize, mediumSize: $mediumSize, largeSize: $largeSize)';
}


}

/// @nodoc
abstract mixin class $VerificationBadgeConfigCopyWith<$Res>  {
  factory $VerificationBadgeConfigCopyWith(VerificationBadgeConfig value, $Res Function(VerificationBadgeConfig) _then) = _$VerificationBadgeConfigCopyWithImpl;
@useResult
$Res call({
 VerificationTier tier, String iconName, String color, String displayName, String description, double smallSize, double mediumSize, double largeSize
});




}
/// @nodoc
class _$VerificationBadgeConfigCopyWithImpl<$Res>
    implements $VerificationBadgeConfigCopyWith<$Res> {
  _$VerificationBadgeConfigCopyWithImpl(this._self, this._then);

  final VerificationBadgeConfig _self;
  final $Res Function(VerificationBadgeConfig) _then;

/// Create a copy of VerificationBadgeConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? tier = null,Object? iconName = null,Object? color = null,Object? displayName = null,Object? description = null,Object? smallSize = null,Object? mediumSize = null,Object? largeSize = null,}) {
  return _then(_self.copyWith(
tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as VerificationTier,iconName: null == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String,color: null == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,smallSize: null == smallSize ? _self.smallSize : smallSize // ignore: cast_nullable_to_non_nullable
as double,mediumSize: null == mediumSize ? _self.mediumSize : mediumSize // ignore: cast_nullable_to_non_nullable
as double,largeSize: null == largeSize ? _self.largeSize : largeSize // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// Adds pattern-matching-related methods to [VerificationBadgeConfig].
extension VerificationBadgeConfigPatterns on VerificationBadgeConfig {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VerificationBadgeConfig value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VerificationBadgeConfig() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VerificationBadgeConfig value)  $default,){
final _that = this;
switch (_that) {
case _VerificationBadgeConfig():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VerificationBadgeConfig value)?  $default,){
final _that = this;
switch (_that) {
case _VerificationBadgeConfig() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( VerificationTier tier,  String iconName,  String color,  String displayName,  String description,  double smallSize,  double mediumSize,  double largeSize)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VerificationBadgeConfig() when $default != null:
return $default(_that.tier,_that.iconName,_that.color,_that.displayName,_that.description,_that.smallSize,_that.mediumSize,_that.largeSize);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( VerificationTier tier,  String iconName,  String color,  String displayName,  String description,  double smallSize,  double mediumSize,  double largeSize)  $default,) {final _that = this;
switch (_that) {
case _VerificationBadgeConfig():
return $default(_that.tier,_that.iconName,_that.color,_that.displayName,_that.description,_that.smallSize,_that.mediumSize,_that.largeSize);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( VerificationTier tier,  String iconName,  String color,  String displayName,  String description,  double smallSize,  double mediumSize,  double largeSize)?  $default,) {final _that = this;
switch (_that) {
case _VerificationBadgeConfig() when $default != null:
return $default(_that.tier,_that.iconName,_that.color,_that.displayName,_that.description,_that.smallSize,_that.mediumSize,_that.largeSize);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VerificationBadgeConfig implements VerificationBadgeConfig {
  const _VerificationBadgeConfig({required this.tier, required this.iconName, required this.color, required this.displayName, required this.description, this.smallSize = 12.0, this.mediumSize = 16.0, this.largeSize = 20.0});
  factory _VerificationBadgeConfig.fromJson(Map<String, dynamic> json) => _$VerificationBadgeConfigFromJson(json);

@override final  VerificationTier tier;
@override final  String iconName;
@override final  String color;
@override final  String displayName;
@override final  String description;
@override@JsonKey() final  double smallSize;
@override@JsonKey() final  double mediumSize;
@override@JsonKey() final  double largeSize;

/// Create a copy of VerificationBadgeConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VerificationBadgeConfigCopyWith<_VerificationBadgeConfig> get copyWith => __$VerificationBadgeConfigCopyWithImpl<_VerificationBadgeConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VerificationBadgeConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VerificationBadgeConfig&&(identical(other.tier, tier) || other.tier == tier)&&(identical(other.iconName, iconName) || other.iconName == iconName)&&(identical(other.color, color) || other.color == color)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.description, description) || other.description == description)&&(identical(other.smallSize, smallSize) || other.smallSize == smallSize)&&(identical(other.mediumSize, mediumSize) || other.mediumSize == mediumSize)&&(identical(other.largeSize, largeSize) || other.largeSize == largeSize));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,tier,iconName,color,displayName,description,smallSize,mediumSize,largeSize);

@override
String toString() {
  return 'VerificationBadgeConfig(tier: $tier, iconName: $iconName, color: $color, displayName: $displayName, description: $description, smallSize: $smallSize, mediumSize: $mediumSize, largeSize: $largeSize)';
}


}

/// @nodoc
abstract mixin class _$VerificationBadgeConfigCopyWith<$Res> implements $VerificationBadgeConfigCopyWith<$Res> {
  factory _$VerificationBadgeConfigCopyWith(_VerificationBadgeConfig value, $Res Function(_VerificationBadgeConfig) _then) = __$VerificationBadgeConfigCopyWithImpl;
@override @useResult
$Res call({
 VerificationTier tier, String iconName, String color, String displayName, String description, double smallSize, double mediumSize, double largeSize
});




}
/// @nodoc
class __$VerificationBadgeConfigCopyWithImpl<$Res>
    implements _$VerificationBadgeConfigCopyWith<$Res> {
  __$VerificationBadgeConfigCopyWithImpl(this._self, this._then);

  final _VerificationBadgeConfig _self;
  final $Res Function(_VerificationBadgeConfig) _then;

/// Create a copy of VerificationBadgeConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? tier = null,Object? iconName = null,Object? color = null,Object? displayName = null,Object? description = null,Object? smallSize = null,Object? mediumSize = null,Object? largeSize = null,}) {
  return _then(_VerificationBadgeConfig(
tier: null == tier ? _self.tier : tier // ignore: cast_nullable_to_non_nullable
as VerificationTier,iconName: null == iconName ? _self.iconName : iconName // ignore: cast_nullable_to_non_nullable
as String,color: null == color ? _self.color : color // ignore: cast_nullable_to_non_nullable
as String,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,smallSize: null == smallSize ? _self.smallSize : smallSize // ignore: cast_nullable_to_non_nullable
as double,mediumSize: null == mediumSize ? _self.mediumSize : mediumSize // ignore: cast_nullable_to_non_nullable
as double,largeSize: null == largeSize ? _self.largeSize : largeSize // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}


/// @nodoc
mixin _$VerificationStats {

 int get totalRequests; int get pendingRequests; int get approvedRequests; int get rejectedRequests; int get billionaireCount; int get celebrityCount; int get generalVerifiedCount; Map<String, int> get requestsByTier; Map<String, int> get requestsByStatus; DateTime get lastUpdated;
/// Create a copy of VerificationStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$VerificationStatsCopyWith<VerificationStats> get copyWith => _$VerificationStatsCopyWithImpl<VerificationStats>(this as VerificationStats, _$identity);

  /// Serializes this VerificationStats to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is VerificationStats&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.pendingRequests, pendingRequests) || other.pendingRequests == pendingRequests)&&(identical(other.approvedRequests, approvedRequests) || other.approvedRequests == approvedRequests)&&(identical(other.rejectedRequests, rejectedRequests) || other.rejectedRequests == rejectedRequests)&&(identical(other.billionaireCount, billionaireCount) || other.billionaireCount == billionaireCount)&&(identical(other.celebrityCount, celebrityCount) || other.celebrityCount == celebrityCount)&&(identical(other.generalVerifiedCount, generalVerifiedCount) || other.generalVerifiedCount == generalVerifiedCount)&&const DeepCollectionEquality().equals(other.requestsByTier, requestsByTier)&&const DeepCollectionEquality().equals(other.requestsByStatus, requestsByStatus)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRequests,pendingRequests,approvedRequests,rejectedRequests,billionaireCount,celebrityCount,generalVerifiedCount,const DeepCollectionEquality().hash(requestsByTier),const DeepCollectionEquality().hash(requestsByStatus),lastUpdated);

@override
String toString() {
  return 'VerificationStats(totalRequests: $totalRequests, pendingRequests: $pendingRequests, approvedRequests: $approvedRequests, rejectedRequests: $rejectedRequests, billionaireCount: $billionaireCount, celebrityCount: $celebrityCount, generalVerifiedCount: $generalVerifiedCount, requestsByTier: $requestsByTier, requestsByStatus: $requestsByStatus, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class $VerificationStatsCopyWith<$Res>  {
  factory $VerificationStatsCopyWith(VerificationStats value, $Res Function(VerificationStats) _then) = _$VerificationStatsCopyWithImpl;
@useResult
$Res call({
 int totalRequests, int pendingRequests, int approvedRequests, int rejectedRequests, int billionaireCount, int celebrityCount, int generalVerifiedCount, Map<String, int> requestsByTier, Map<String, int> requestsByStatus, DateTime lastUpdated
});




}
/// @nodoc
class _$VerificationStatsCopyWithImpl<$Res>
    implements $VerificationStatsCopyWith<$Res> {
  _$VerificationStatsCopyWithImpl(this._self, this._then);

  final VerificationStats _self;
  final $Res Function(VerificationStats) _then;

/// Create a copy of VerificationStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalRequests = null,Object? pendingRequests = null,Object? approvedRequests = null,Object? rejectedRequests = null,Object? billionaireCount = null,Object? celebrityCount = null,Object? generalVerifiedCount = null,Object? requestsByTier = null,Object? requestsByStatus = null,Object? lastUpdated = null,}) {
  return _then(_self.copyWith(
totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,pendingRequests: null == pendingRequests ? _self.pendingRequests : pendingRequests // ignore: cast_nullable_to_non_nullable
as int,approvedRequests: null == approvedRequests ? _self.approvedRequests : approvedRequests // ignore: cast_nullable_to_non_nullable
as int,rejectedRequests: null == rejectedRequests ? _self.rejectedRequests : rejectedRequests // ignore: cast_nullable_to_non_nullable
as int,billionaireCount: null == billionaireCount ? _self.billionaireCount : billionaireCount // ignore: cast_nullable_to_non_nullable
as int,celebrityCount: null == celebrityCount ? _self.celebrityCount : celebrityCount // ignore: cast_nullable_to_non_nullable
as int,generalVerifiedCount: null == generalVerifiedCount ? _self.generalVerifiedCount : generalVerifiedCount // ignore: cast_nullable_to_non_nullable
as int,requestsByTier: null == requestsByTier ? _self.requestsByTier : requestsByTier // ignore: cast_nullable_to_non_nullable
as Map<String, int>,requestsByStatus: null == requestsByStatus ? _self.requestsByStatus : requestsByStatus // ignore: cast_nullable_to_non_nullable
as Map<String, int>,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

}


/// Adds pattern-matching-related methods to [VerificationStats].
extension VerificationStatsPatterns on VerificationStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _VerificationStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _VerificationStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _VerificationStats value)  $default,){
final _that = this;
switch (_that) {
case _VerificationStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _VerificationStats value)?  $default,){
final _that = this;
switch (_that) {
case _VerificationStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalRequests,  int pendingRequests,  int approvedRequests,  int rejectedRequests,  int billionaireCount,  int celebrityCount,  int generalVerifiedCount,  Map<String, int> requestsByTier,  Map<String, int> requestsByStatus,  DateTime lastUpdated)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _VerificationStats() when $default != null:
return $default(_that.totalRequests,_that.pendingRequests,_that.approvedRequests,_that.rejectedRequests,_that.billionaireCount,_that.celebrityCount,_that.generalVerifiedCount,_that.requestsByTier,_that.requestsByStatus,_that.lastUpdated);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalRequests,  int pendingRequests,  int approvedRequests,  int rejectedRequests,  int billionaireCount,  int celebrityCount,  int generalVerifiedCount,  Map<String, int> requestsByTier,  Map<String, int> requestsByStatus,  DateTime lastUpdated)  $default,) {final _that = this;
switch (_that) {
case _VerificationStats():
return $default(_that.totalRequests,_that.pendingRequests,_that.approvedRequests,_that.rejectedRequests,_that.billionaireCount,_that.celebrityCount,_that.generalVerifiedCount,_that.requestsByTier,_that.requestsByStatus,_that.lastUpdated);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalRequests,  int pendingRequests,  int approvedRequests,  int rejectedRequests,  int billionaireCount,  int celebrityCount,  int generalVerifiedCount,  Map<String, int> requestsByTier,  Map<String, int> requestsByStatus,  DateTime lastUpdated)?  $default,) {final _that = this;
switch (_that) {
case _VerificationStats() when $default != null:
return $default(_that.totalRequests,_that.pendingRequests,_that.approvedRequests,_that.rejectedRequests,_that.billionaireCount,_that.celebrityCount,_that.generalVerifiedCount,_that.requestsByTier,_that.requestsByStatus,_that.lastUpdated);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _VerificationStats implements VerificationStats {
  const _VerificationStats({required this.totalRequests, required this.pendingRequests, required this.approvedRequests, required this.rejectedRequests, required this.billionaireCount, required this.celebrityCount, required this.generalVerifiedCount, required final  Map<String, int> requestsByTier, required final  Map<String, int> requestsByStatus, required this.lastUpdated}): _requestsByTier = requestsByTier,_requestsByStatus = requestsByStatus;
  factory _VerificationStats.fromJson(Map<String, dynamic> json) => _$VerificationStatsFromJson(json);

@override final  int totalRequests;
@override final  int pendingRequests;
@override final  int approvedRequests;
@override final  int rejectedRequests;
@override final  int billionaireCount;
@override final  int celebrityCount;
@override final  int generalVerifiedCount;
 final  Map<String, int> _requestsByTier;
@override Map<String, int> get requestsByTier {
  if (_requestsByTier is EqualUnmodifiableMapView) return _requestsByTier;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requestsByTier);
}

 final  Map<String, int> _requestsByStatus;
@override Map<String, int> get requestsByStatus {
  if (_requestsByStatus is EqualUnmodifiableMapView) return _requestsByStatus;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(_requestsByStatus);
}

@override final  DateTime lastUpdated;

/// Create a copy of VerificationStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$VerificationStatsCopyWith<_VerificationStats> get copyWith => __$VerificationStatsCopyWithImpl<_VerificationStats>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$VerificationStatsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _VerificationStats&&(identical(other.totalRequests, totalRequests) || other.totalRequests == totalRequests)&&(identical(other.pendingRequests, pendingRequests) || other.pendingRequests == pendingRequests)&&(identical(other.approvedRequests, approvedRequests) || other.approvedRequests == approvedRequests)&&(identical(other.rejectedRequests, rejectedRequests) || other.rejectedRequests == rejectedRequests)&&(identical(other.billionaireCount, billionaireCount) || other.billionaireCount == billionaireCount)&&(identical(other.celebrityCount, celebrityCount) || other.celebrityCount == celebrityCount)&&(identical(other.generalVerifiedCount, generalVerifiedCount) || other.generalVerifiedCount == generalVerifiedCount)&&const DeepCollectionEquality().equals(other._requestsByTier, _requestsByTier)&&const DeepCollectionEquality().equals(other._requestsByStatus, _requestsByStatus)&&(identical(other.lastUpdated, lastUpdated) || other.lastUpdated == lastUpdated));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,totalRequests,pendingRequests,approvedRequests,rejectedRequests,billionaireCount,celebrityCount,generalVerifiedCount,const DeepCollectionEquality().hash(_requestsByTier),const DeepCollectionEquality().hash(_requestsByStatus),lastUpdated);

@override
String toString() {
  return 'VerificationStats(totalRequests: $totalRequests, pendingRequests: $pendingRequests, approvedRequests: $approvedRequests, rejectedRequests: $rejectedRequests, billionaireCount: $billionaireCount, celebrityCount: $celebrityCount, generalVerifiedCount: $generalVerifiedCount, requestsByTier: $requestsByTier, requestsByStatus: $requestsByStatus, lastUpdated: $lastUpdated)';
}


}

/// @nodoc
abstract mixin class _$VerificationStatsCopyWith<$Res> implements $VerificationStatsCopyWith<$Res> {
  factory _$VerificationStatsCopyWith(_VerificationStats value, $Res Function(_VerificationStats) _then) = __$VerificationStatsCopyWithImpl;
@override @useResult
$Res call({
 int totalRequests, int pendingRequests, int approvedRequests, int rejectedRequests, int billionaireCount, int celebrityCount, int generalVerifiedCount, Map<String, int> requestsByTier, Map<String, int> requestsByStatus, DateTime lastUpdated
});




}
/// @nodoc
class __$VerificationStatsCopyWithImpl<$Res>
    implements _$VerificationStatsCopyWith<$Res> {
  __$VerificationStatsCopyWithImpl(this._self, this._then);

  final _VerificationStats _self;
  final $Res Function(_VerificationStats) _then;

/// Create a copy of VerificationStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalRequests = null,Object? pendingRequests = null,Object? approvedRequests = null,Object? rejectedRequests = null,Object? billionaireCount = null,Object? celebrityCount = null,Object? generalVerifiedCount = null,Object? requestsByTier = null,Object? requestsByStatus = null,Object? lastUpdated = null,}) {
  return _then(_VerificationStats(
totalRequests: null == totalRequests ? _self.totalRequests : totalRequests // ignore: cast_nullable_to_non_nullable
as int,pendingRequests: null == pendingRequests ? _self.pendingRequests : pendingRequests // ignore: cast_nullable_to_non_nullable
as int,approvedRequests: null == approvedRequests ? _self.approvedRequests : approvedRequests // ignore: cast_nullable_to_non_nullable
as int,rejectedRequests: null == rejectedRequests ? _self.rejectedRequests : rejectedRequests // ignore: cast_nullable_to_non_nullable
as int,billionaireCount: null == billionaireCount ? _self.billionaireCount : billionaireCount // ignore: cast_nullable_to_non_nullable
as int,celebrityCount: null == celebrityCount ? _self.celebrityCount : celebrityCount // ignore: cast_nullable_to_non_nullable
as int,generalVerifiedCount: null == generalVerifiedCount ? _self.generalVerifiedCount : generalVerifiedCount // ignore: cast_nullable_to_non_nullable
as int,requestsByTier: null == requestsByTier ? _self._requestsByTier : requestsByTier // ignore: cast_nullable_to_non_nullable
as Map<String, int>,requestsByStatus: null == requestsByStatus ? _self._requestsByStatus : requestsByStatus // ignore: cast_nullable_to_non_nullable
as Map<String, int>,lastUpdated: null == lastUpdated ? _self.lastUpdated : lastUpdated // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}


}

// dart format on
