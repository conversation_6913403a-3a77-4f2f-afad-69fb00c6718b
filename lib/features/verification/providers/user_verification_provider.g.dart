// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_verification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userVerificationStatusHash() =>
    r'475adee3dae0b3de9ea3770bfcb09da466733515';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Provider to get verification status for any user by ID
///
/// Copied from [userVerificationStatus].
@ProviderFor(userVerificationStatus)
const userVerificationStatusProvider = UserVerificationStatusFamily();

/// Provider to get verification status for any user by ID
///
/// Copied from [userVerificationStatus].
class UserVerificationStatusFamily
    extends Family<AsyncValue<UserVerificationStatus>> {
  /// Provider to get verification status for any user by ID
  ///
  /// Copied from [userVerificationStatus].
  const UserVerificationStatusFamily();

  /// Provider to get verification status for any user by ID
  ///
  /// Copied from [userVerificationStatus].
  UserVerificationStatusProvider call(String userId) {
    return UserVerificationStatusProvider(userId);
  }

  @override
  UserVerificationStatusProvider getProviderOverride(
    covariant UserVerificationStatusProvider provider,
  ) {
    return call(provider.userId);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'userVerificationStatusProvider';
}

/// Provider to get verification status for any user by ID
///
/// Copied from [userVerificationStatus].
class UserVerificationStatusProvider
    extends AutoDisposeFutureProvider<UserVerificationStatus> {
  /// Provider to get verification status for any user by ID
  ///
  /// Copied from [userVerificationStatus].
  UserVerificationStatusProvider(String userId)
    : this._internal(
        (ref) =>
            userVerificationStatus(ref as UserVerificationStatusRef, userId),
        from: userVerificationStatusProvider,
        name: r'userVerificationStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$userVerificationStatusHash,
        dependencies: UserVerificationStatusFamily._dependencies,
        allTransitiveDependencies:
            UserVerificationStatusFamily._allTransitiveDependencies,
        userId: userId,
      );

  UserVerificationStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userId,
  }) : super.internal();

  final String userId;

  @override
  Override overrideWith(
    FutureOr<UserVerificationStatus> Function(
      UserVerificationStatusRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: UserVerificationStatusProvider._internal(
        (ref) => create(ref as UserVerificationStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userId: userId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<UserVerificationStatus> createElement() {
    return _UserVerificationStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is UserVerificationStatusProvider && other.userId == userId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin UserVerificationStatusRef
    on AutoDisposeFutureProviderRef<UserVerificationStatus> {
  /// The parameter `userId` of this provider.
  String get userId;
}

class _UserVerificationStatusProviderElement
    extends AutoDisposeFutureProviderElement<UserVerificationStatus>
    with UserVerificationStatusRef {
  _UserVerificationStatusProviderElement(super.provider);

  @override
  String get userId => (origin as UserVerificationStatusProvider).userId;
}

String _$batchUserVerificationStatusHash() =>
    r'2381a89a84131f0d568fe4e8a64fb3437ec677a6';

/// Provider for batch verification status lookup
///
/// Copied from [batchUserVerificationStatus].
@ProviderFor(batchUserVerificationStatus)
const batchUserVerificationStatusProvider = BatchUserVerificationStatusFamily();

/// Provider for batch verification status lookup
///
/// Copied from [batchUserVerificationStatus].
class BatchUserVerificationStatusFamily
    extends Family<AsyncValue<Map<String, UserVerificationStatus>>> {
  /// Provider for batch verification status lookup
  ///
  /// Copied from [batchUserVerificationStatus].
  const BatchUserVerificationStatusFamily();

  /// Provider for batch verification status lookup
  ///
  /// Copied from [batchUserVerificationStatus].
  BatchUserVerificationStatusProvider call(List<String> userIds) {
    return BatchUserVerificationStatusProvider(userIds);
  }

  @override
  BatchUserVerificationStatusProvider getProviderOverride(
    covariant BatchUserVerificationStatusProvider provider,
  ) {
    return call(provider.userIds);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'batchUserVerificationStatusProvider';
}

/// Provider for batch verification status lookup
///
/// Copied from [batchUserVerificationStatus].
class BatchUserVerificationStatusProvider
    extends AutoDisposeFutureProvider<Map<String, UserVerificationStatus>> {
  /// Provider for batch verification status lookup
  ///
  /// Copied from [batchUserVerificationStatus].
  BatchUserVerificationStatusProvider(List<String> userIds)
    : this._internal(
        (ref) => batchUserVerificationStatus(
          ref as BatchUserVerificationStatusRef,
          userIds,
        ),
        from: batchUserVerificationStatusProvider,
        name: r'batchUserVerificationStatusProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$batchUserVerificationStatusHash,
        dependencies: BatchUserVerificationStatusFamily._dependencies,
        allTransitiveDependencies:
            BatchUserVerificationStatusFamily._allTransitiveDependencies,
        userIds: userIds,
      );

  BatchUserVerificationStatusProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.userIds,
  }) : super.internal();

  final List<String> userIds;

  @override
  Override overrideWith(
    FutureOr<Map<String, UserVerificationStatus>> Function(
      BatchUserVerificationStatusRef provider,
    )
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: BatchUserVerificationStatusProvider._internal(
        (ref) => create(ref as BatchUserVerificationStatusRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        userIds: userIds,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, UserVerificationStatus>>
  createElement() {
    return _BatchUserVerificationStatusProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is BatchUserVerificationStatusProvider &&
        other.userIds == userIds;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, userIds.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin BatchUserVerificationStatusRef
    on AutoDisposeFutureProviderRef<Map<String, UserVerificationStatus>> {
  /// The parameter `userIds` of this provider.
  List<String> get userIds;
}

class _BatchUserVerificationStatusProviderElement
    extends
        AutoDisposeFutureProviderElement<Map<String, UserVerificationStatus>>
    with BatchUserVerificationStatusRef {
  _BatchUserVerificationStatusProviderElement(super.provider);

  @override
  List<String> get userIds =>
      (origin as BatchUserVerificationStatusProvider).userIds;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
